{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/accessManagement.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"access_management\": \"accessManagement-module-scss-module__AuP3Pq__access_management\",\n  \"active\": \"accessManagement-module-scss-module__AuP3Pq__active\",\n  \"add_employee_height\": \"accessManagement-module-scss-module__AuP3Pq__add_employee_height\",\n  \"benefit_text\": \"accessManagement-module-scss-module__AuP3Pq__benefit_text\",\n  \"checkmark\": \"accessManagement-module-scss-module__AuP3Pq__checkmark\",\n  \"custom_dropdown\": \"accessManagement-module-scss-module__AuP3Pq__custom_dropdown\",\n  \"department_card\": \"accessManagement-module-scss-module__AuP3Pq__department_card\",\n  \"description_cell\": \"accessManagement-module-scss-module__AuP3Pq__description_cell\",\n  \"disabled\": \"accessManagement-module-scss-module__AuP3Pq__disabled\",\n  \"disabled_role\": \"accessManagement-module-scss-module__AuP3Pq__disabled_role\",\n  \"dropdown_arrow\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_arrow\",\n  \"dropdown_divider\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_divider\",\n  \"dropdown_item\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_item\",\n  \"fadeIn\": \"accessManagement-module-scss-module__AuP3Pq__fadeIn\",\n  \"fixed_height\": \"accessManagement-module-scss-module__AuP3Pq__fixed_height\",\n  \"flex_content\": \"accessManagement-module-scss-module__AuP3Pq__flex_content\",\n  \"folder_container\": \"accessManagement-module-scss-module__AuP3Pq__folder_container\",\n  \"form_card\": \"accessManagement-module-scss-module__AuP3Pq__form_card\",\n  \"plan_name\": \"accessManagement-module-scss-module__AuP3Pq__plan_name\",\n  \"plan_option\": \"accessManagement-module-scss-module__AuP3Pq__plan_option\",\n  \"plan_price\": \"accessManagement-module-scss-module__AuP3Pq__plan_price\",\n  \"price_type\": \"accessManagement-module-scss-module__AuP3Pq__price_type\",\n  \"role_dropdown\": \"accessManagement-module-scss-module__AuP3Pq__role_dropdown\",\n  \"role_option\": \"accessManagement-module-scss-module__AuP3Pq__role_option\",\n  \"role_select\": \"accessManagement-module-scss-module__AuP3Pq__role_select\",\n  \"role_select_employee\": \"accessManagement-module-scss-module__AuP3Pq__role_select_employee\",\n  \"role_selector\": \"accessManagement-module-scss-module__AuP3Pq__role_selector\",\n  \"save_badge\": \"accessManagement-module-scss-module__AuP3Pq__save_badge\",\n  \"section_title\": \"accessManagement-module-scss-module__AuP3Pq__section_title\",\n  \"select_plan\": \"accessManagement-module-scss-module__AuP3Pq__select_plan\",\n  \"select_plan_section\": \"accessManagement-module-scss-module__AuP3Pq__select_plan_section\",\n  \"selected\": \"accessManagement-module-scss-module__AuP3Pq__selected\",\n  \"selected_plan\": \"accessManagement-module-scss-module__AuP3Pq__selected_plan\",\n  \"selected_role\": \"accessManagement-module-scss-module__AuP3Pq__selected_role\",\n  \"show_below\": \"accessManagement-module-scss-module__AuP3Pq__show_below\",\n  \"sibling_height\": \"accessManagement-module-scss-module__AuP3Pq__sibling_height\",\n  \"side_bar\": \"accessManagement-module-scss-module__AuP3Pq__side_bar\",\n  \"subscription_benefit_text\": \"accessManagement-module-scss-module__AuP3Pq__subscription_benefit_text\",\n  \"subscription_option\": \"accessManagement-module-scss-module__AuP3Pq__subscription_option\",\n  \"subscription_page\": \"accessManagement-module-scss-module__AuP3Pq__subscription_page\",\n  \"subscription_plan\": \"accessManagement-module-scss-module__AuP3Pq__subscription_plan\",\n  \"subscription_plan_card\": \"accessManagement-module-scss-module__AuP3Pq__subscription_plan_card\",\n  \"tip_para\": \"accessManagement-module-scss-module__AuP3Pq__tip_para\",\n  \"user_roles\": \"accessManagement-module-scss-module__AuP3Pq__user_roles\",\n  \"user_roles_img\": \"accessManagement-module-scss-module__AuP3Pq__user_roles_img\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC,8JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/SearchIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction SearchIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\">\n      <g opacity=\"0.7\">\n        <path\n          d=\"M28.2109 18.8274C28.2109 20.6833 27.6605 22.4976 26.6295 24.0407C25.5984 25.5839 24.1329 26.7867 22.4182 27.497C20.7036 28.2072 18.8168 28.3931 16.9965 28.0311C15.1762 27.6691 13.5042 26.7755 12.1917 25.4632C10.8793 24.1509 9.9855 22.479 9.62331 20.6587C9.26111 18.8384 9.4468 16.9517 10.1569 15.237C10.867 13.5222 12.0696 12.0566 13.6127 11.0253C15.1557 9.99409 16.9699 9.44356 18.8259 9.44336C20.0583 9.44323 21.2786 9.68586 22.4173 10.1574C23.5559 10.6289 24.5905 11.3201 25.462 12.1915C26.3335 13.0629 27.0248 14.0974 27.4965 15.236C27.9681 16.3746 28.2109 17.595 28.2109 18.8274Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path d=\"M30.557 30.559L25.457 25.459\" stroke=\"#333333\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n      </g>\n    </svg>\n  );\n}\n\nexport default SearchIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YAAE,SAAQ;;8BACT,8OAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEjB,8OAAC;oBAAK,GAAE;oBAA+B,QAAO;oBAAU,aAAY;oBAAM,eAAc;oBAAQ,gBAAe;;;;;;;;;;;;;;;;;AAIvH;uCAEe", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ThreeDotsIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction ThreeDotsIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.0001 19.2C13.3256 19.2 14.4001 20.2745 14.4001 21.6C14.4001 22.9255 13.3256 24 12.0001 24C10.6746 24 9.6001 22.9255 9.6001 21.6C9.6001 20.2745 10.6746 19.2 12.0001 19.2ZM12.0001 9.60005C13.3256 9.60005 14.4001 10.6746 14.4001 12C14.4001 13.3255 13.3256 14.4 12.0001 14.4C10.6746 14.4 9.6001 13.3255 9.6001 12C9.6001 10.6746 10.6746 9.60005 12.0001 9.60005ZM12.0001 0C13.3256 0 14.4001 1.07452 14.4001 2.39999C14.4001 3.72546 13.3256 4.79998 12.0001 4.79998C10.6746 4.79998 9.6001 3.72546 9.6001 2.39999C9.6001 1.07452 10.6746 0 12.0001 0Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default ThreeDotsIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/FolderColorIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction FolderColorIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"184\" height=\"126\" viewBox=\"0 0 184 126\" fill=\"none\" className={className}>\n      <g filter=\"url(#filter0_ddi_10045_8703)\">\n        <path\n          d=\"M5 10.4446C5 5.96629 8.63036 2.33594 13.1086 2.33594H38.6011C41.6724 2.33594 44.4801 4.0712 45.8537 6.81827L51.0244 17.1598C52.398 19.9069 55.2057 21.6422 58.277 21.6422H170.648C175.126 21.6422 178.756 25.2725 178.756 29.7508V110.065C178.756 114.543 175.126 118.173 170.648 118.173H13.1086C8.63036 118.173 5 114.543 5 110.065V10.4446Z\"\n          fill=\"#436EB6\"\n        />\n      </g>\n      <defs>\n        <filter\n          id=\"filter0_ddi_10045_8703\"\n          x=\"0.366501\"\n          y=\"-13.8813\"\n          width=\"183.023\"\n          height=\"139.003\"\n          filterUnits=\"userSpaceOnUse\"\n          color-interpolation-filters=\"sRGB\"\n        >\n          <feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\" />\n          <feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\" />\n          <feOffset dy=\"2.31675\" />\n          <feGaussianBlur stdDeviation=\"2.31675\" />\n          <feColorMatrix type=\"matrix\" values=\"0 0 0 0 0.172549 0 0 0 0 0.168627 0 0 0 0 0.164706 0 0 0 0.1 0\" />\n          <feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_10045_8703\" />\n          <feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\" />\n          <feOffset dx=\"1.15837\" dy=\"1.15837\" />\n          <feGaussianBlur stdDeviation=\"0.579187\" />\n          <feComposite in2=\"hardAlpha\" operator=\"out\" />\n          <feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0\" />\n          <feBlend mode=\"normal\" in2=\"effect1_dropShadow_10045_8703\" result=\"effect2_dropShadow_10045_8703\" />\n          <feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect2_dropShadow_10045_8703\" result=\"shape\" />\n          <feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\" />\n          <feOffset dy=\"-16.2172\" />\n          <feGaussianBlur stdDeviation=\"12.1629\" />\n          <feComposite in2=\"hardAlpha\" operator=\"arithmetic\" k2=\"-1\" k3=\"1\" />\n          <feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0\" />\n          <feBlend mode=\"normal\" in2=\"shape\" result=\"effect3_innerShadow_10045_8703\" />\n        </filter>\n      </defs>\n    </svg>\n  );\n}\n\nexport default FolderColorIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IAC5D,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAM,QAAO;QAAM,SAAQ;QAAc,MAAK;QAAO,WAAW;;0BAC5G,8OAAC;gBAAE,QAAO;0BACR,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,aAAY;oBACZ,+BAA4B;;sCAE5B,8OAAC;4BAAQ,iBAAc;4BAAI,QAAO;;;;;;sCAClC,8OAAC;4BAAc,IAAG;4BAAc,MAAK;4BAAS,QAAO;4BAA4C,QAAO;;;;;;sCACxG,8OAAC;4BAAS,IAAG;;;;;;sCACb,8OAAC;4BAAe,cAAa;;;;;;sCAC7B,8OAAC;4BAAc,MAAK;4BAAS,QAAO;;;;;;sCACpC,8OAAC;4BAAQ,MAAK;4BAAS,KAAI;4BAAqB,QAAO;;;;;;sCACvD,8OAAC;4BAAc,IAAG;4BAAc,MAAK;4BAAS,QAAO;4BAA4C,QAAO;;;;;;sCACxG,8OAAC;4BAAS,IAAG;4BAAU,IAAG;;;;;;sCAC1B,8OAAC;4BAAe,cAAa;;;;;;sCAC7B,8OAAC;4BAAY,KAAI;4BAAY,UAAS;;;;;;sCACtC,8OAAC;4BAAc,MAAK;4BAAS,QAAO;;;;;;sCACpC,8OAAC;4BAAQ,MAAK;4BAAS,KAAI;4BAAgC,QAAO;;;;;;sCAClE,8OAAC;4BAAQ,MAAK;4BAAS,IAAG;4BAAgB,KAAI;4BAAgC,QAAO;;;;;;sCACrF,8OAAC;4BAAc,IAAG;4BAAc,MAAK;4BAAS,QAAO;4BAA4C,QAAO;;;;;;sCACxG,8OAAC;4BAAS,IAAG;;;;;;sCACb,8OAAC;4BAAe,cAAa;;;;;;sCAC7B,8OAAC;4BAAY,KAAI;4BAAY,UAAS;4BAAa,IAAG;4BAAK,IAAG;;;;;;sCAC9D,8OAAC;4BAAc,MAAK;4BAAS,QAAO;;;;;;sCACpC,8OAAC;4BAAQ,MAAK;4BAAS,KAAI;4BAAQ,QAAO;;;;;;;;;;;;;;;;;;;;;;;AAKpD;uCAEe", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/departmentService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport * as http from \"@/utils/http\";\n\nexport interface IDepartmentAlter {\n  id: number;\n  name: string;\n  organizationId: number;\n}\n\nexport interface FindDepartmentResponse {\n  id: number;\n  name: string;\n  isDefaultDepartment: boolean;\n}\n\n/**\n * Get all departments associated with an organization\n * @param search Optional search query string\n * @returns Promise with API response\n */\nexport const findDepartments = (search?: string): Promise<IApiResponseCommonInterface<FindDepartmentResponse[]>> => {\n  return http.get<{ search?: string }, IApiResponseCommonInterface<FindDepartmentResponse[]>>(endpoint.departments.GET_DEPARTMENTS, {\n    search,\n  });\n};\n\n/**\n * Add a new department\n * @param departmentData Department data to add\n * @returns Promise with API response\n */\nexport const addDepartment = (departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  return http.post(endpoint.departments.ADD_DEPARTMENT, departmentData);\n};\n\n/**\n * Update an existing department\n * @param departmentId ID of the department to update\n * @param departmentData Updated department data\n * @returns Promise with API response\n */\nexport const updateDepartment = (departmentId: number, departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  const url = endpoint.departments.UPDATE_DEPARTMENT.replace(\":departmentId\", departmentId.toString());\n\n  return http.put(url, departmentData);\n};\n\n/**\n * Delete a department\n * @param departmentId ID of the department to delete\n * @param organizationId ID of the organization\n * @returns Promise with API response\n */\nexport const deleteDepartment = (departmentId: number): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  const url = endpoint.departments.DELETE_DEPARTMENT.replace(\":departmentId\", departmentId.toString());\n\n  return http.remove(url);\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;;;AAmBO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAA8E,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,eAAe,EAAE;QAChI;IACF;AACF;AAOO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,cAAc,EAAE;AACxD;AAQO,MAAM,mBAAmB,CAAC,cAAsB;IACrD,MAAM,MAAM,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,aAAa,QAAQ;IAEjG,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,KAAK;AACvB;AAQO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,MAAM,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,aAAa,QAAQ;IAEjG,OAAO,CAAA,GAAA,oHAAA,CAAA,SAAW,AAAD,EAAE;AACrB", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/validationSchema.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\n// Regex patterns\nexport const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\n\n// Name validation regex - only allows alphabetic characters (including Unicode letters) and spaces\n// This pattern supports international names while rejecting digits and special characters\nexport const CANDIDATE_NAME_REGEX = /^[\\p{L}\\s]+$/u;\n\n// Employee validation schema\nexport const employeeValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    firstName: yup\n      .string()\n      .trim()\n      .required(translation(\"first_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_first_name\"))\n      .max(50, translation(\"max_first_name\")),\n    lastName: yup\n      .string()\n      .trim()\n      .required(translation(\"last_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_last_name\"))\n      .max(50, translation(\"max_last_name\")),\n    email: yup\n      .string()\n      .trim()\n      .required(translation(\"email_req\"))\n      .email(translation(\"email_val_msg\"))\n      .matches(EMAIL_REGEX, translation(\"email_val_msg\")),\n    department: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"department_req\"))\n      .min(1, \"Department must be selected\"),\n    role: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"role_req\"))\n      .min(1, \"Role must be selected\"),\n    // orderOfInterview: yup.string().required(translation(\"order_interview_req\")),\n  });\n\n// Employee array validation schema\nexport const employeesValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    employees: yup.array().of(employeeValidationSchema(translation)).required(\"At least one employee is required\"),\n  });\n\n// Department validation schema\nexport const departmentValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"department_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_department_name\"))\n      .max(50, translation(\"max_department_name\")),\n  });\n\n// Role validation schema\nexport const roleValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"role_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_role_name\"))\n      .max(50, translation(\"max_role_name\")),\n  });\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,cAAc;AACpB,MAAM,aAAa;AAInB,MAAM,uBAAuB;AAG7B,MAAM,2BAA2B,CAAC,cACvC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,mIAAA,CAAA,SACF,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,mBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,mBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,UAAU,CAAA,GAAA,mIAAA,CAAA,SACD,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,OAAO,CAAA,GAAA,mIAAA,CAAA,SACE,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,cACrB,KAAK,CAAC,YAAY,kBAClB,OAAO,CAAC,aAAa,YAAY;QACpC,YAAY,CAAA,GAAA,mIAAA,CAAA,SACH,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,mBACrB,GAAG,CAAC,GAAG;QACV,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,aACrB,GAAG,CAAC,GAAG;IAEZ;AAGK,MAAM,4BAA4B,CAAC,cACxC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,mIAAA,CAAA,QAAS,AAAD,IAAI,EAAE,CAAC,yBAAyB,cAAc,QAAQ,CAAC;IAC5E;AAGK,MAAM,6BAA6B,CAAC,cACzC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,wBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,wBACnB,GAAG,CAAC,IAAI,YAAY;IACzB;AAGK,MAAM,uBAAuB,CAAC,cACnC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;IACzB", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/accessManagement/EmployeeManagement.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useState } from \"react\";\n\nimport styles from \"../../../styles/accessManagement.module.scss\";\nimport Button from \"@/components/formElements/Button\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport Textbox from \"@/components/formElements/Textbox\";\nimport SearchIcon from \"@/components/svgComponents/SearchIcon\";\nimport { useForm } from \"react-hook-form\";\nimport ThreeDotsIcon from \"@/components/svgComponents/ThreeDotsIcon\";\nimport FolderColorIcon from \"@/components/svgComponents/FolderColorIcon\";\n// import UserRoleModal from \"@/components/commonModals/UserRoleModal\";\nimport DepartmentModal from \"@/components/commonModals/DepartmentModal\";\nimport { FindDepartmentResponse, findDepartments } from \"@/services/departmentService\";\nimport { useRouter } from \"next/navigation\";\nimport routes from \"@/constants/routes\";\nimport { toastMessageSuccess, toastMessageError } from \"@/utils/helper\";\nimport { useTranslations } from \"next-intl\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\nexport const DEPARTMENT_ALTER_MODE = {\n  ADD: \"add\",\n  EDIT: \"edit\",\n  DELETE: \"delete\",\n};\n\nconst EmployeeManagement = () => {\n  const router = useRouter();\n  const { control, watch } = useForm<{ search: string }>({ defaultValues: { search: \"\" } });\n  const t = useTranslations();\n\n  // const [roleModalConfig, setRoleModalConfig] = useState<{\n  //   show: boolean;\n  //   mode: (typeof EMPLOYEE_ALTER_MODE)[keyof typeof EMPLOYEE_ALTER_MODE];\n  //   role: { id: number; name: string } | null;\n  // }>({ show: false, mode: EMPLOYEE_ALTER_MODE.EDIT, role: null });\n\n  const [departments, setDepartments] = useState<FindDepartmentResponse[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeDepartmentMenu, setActiveDepartmentMenu] = useState<number | null>(null);\n\n  const [departmentModalConfig, setDepartmentModalConfig] = useState<{\n    show: boolean;\n    mode: (typeof DEPARTMENT_ALTER_MODE)[keyof typeof DEPARTMENT_ALTER_MODE];\n    department: FindDepartmentResponse | null;\n  }>({ show: false, mode: DEPARTMENT_ALTER_MODE.ADD, department: null });\n\n  const searchValue = watch(\"search\");\n\n  // Fetch departments on component mount and when search value changes\n  useEffect(() => {\n    const debounceTimer = setTimeout(() => {\n      fetchDepartments(searchValue);\n    }, 500); // Debounce search for 500ms\n\n    return () => clearTimeout(debounceTimer);\n  }, [searchValue]);\n\n  // Function to fetch departments\n  const fetchDepartments = async (search?: string) => {\n    try {\n      setIsLoading(true);\n      const response = await findDepartments(search);\n      const result = response.data;\n\n      if (result && result.success && result.data) {\n        // Check if departments is directly in data or nested\n        if (Array.isArray(result.data)) {\n          setDepartments(result.data);\n        } else {\n          toastMessageError(t(result.message || \"failed_load_departments\"));\n          setDepartments([]);\n        }\n      } else {\n        toastMessageError(t(result.message || \"failed_load_departments\"));\n        setDepartments([]);\n      }\n    } catch (error) {\n      console.error(error);\n      toastMessageError(t(\"error_fetching_departments\"));\n      setDepartments([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Function to open department modal\n  const openDepartmentModal = (\n    mode: (typeof DEPARTMENT_ALTER_MODE)[keyof typeof DEPARTMENT_ALTER_MODE],\n    department: FindDepartmentResponse | null = null\n  ) => {\n    setDepartmentModalConfig({\n      show: true,\n      mode,\n      department,\n    });\n  };\n\n  // Function to close department modal\n  const closeDepartmentModal = () => {\n    setDepartmentModalConfig({\n      show: false,\n      mode: DEPARTMENT_ALTER_MODE.ADD,\n      department: null,\n    });\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      // Check if the click was outside any department action menu\n      const target = event.target as HTMLElement;\n      if (!target.closest(\".department-actions\")) {\n        setActiveDepartmentMenu(null);\n      }\n    };\n\n    // Add the event listener\n    document.addEventListener(\"mousedown\", handleClickOutside);\n\n    // Clean up\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n  return (\n    <>\n      <section className={styles.access_management}>\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-md-12\">\n              <div className=\"button-align justify-content-between\">\n                <div className=\"common-page-head-section\">\n                  <div className=\"main-heading\">\n                    <h2>\n                      Employee <span>Management</span>\n                    </h2>\n                  </div>\n                </div>\n                <div className=\"button-align justify-content-end w-50\">\n                  <InputWrapper className=\"mb-0 w-100\">\n                    <div className=\"icon-align right\">\n                      <Textbox className=\"form-control w-100\" control={control} name=\"search\" type=\"text\" placeholder={t(\"search_department\")}>\n                        <InputWrapper.Icon>\n                          <SearchIcon />\n                        </InputWrapper.Icon>\n                      </Textbox>\n                    </div>\n                  </InputWrapper>\n\n                  <Button className=\"primary-btn rounded-md button-sm\" onClick={() => openDepartmentModal(DEPARTMENT_ALTER_MODE.ADD)}>\n                    {t(\"add_new_department\")}\n                  </Button>\n                </div>\n              </div>\n\n              {isLoading ? (\n                <ul className=\"library-folder w-100\">\n                  {Array.from({ length: 12 }).map((_, index) => (\n                    <li key={index} className=\"folder-card p-0 border-0  \">\n                      <Skeleton height={165} width=\"100%\" borderRadius={10} inline={true} />\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <ul className=\"library-folder w-100\">\n                  {departments.length === 0 ? (\n                    <div className=\"d-flex justify-content-center w-100\">\n                      <div className=\"text-center py-5\">\n                        <p>\n                          <strong>{searchValue ? t(\"no_department_search_results\") : t(\"no_departments_found\")}</strong>\n                        </p>\n                      </div>\n                    </div>\n                  ) : (\n                    departments.map((department) => (\n                      <li className=\"folder-card\" key={department.id}>\n                        <div className=\"position-relative\">\n                          <div className=\"department-actions\">\n                            <Button\n                              className={`clear-btn p-0 ${department.isDefaultDepartment ? \"disabled opacity-0\" : \"\"}`}\n                              onClick={(e) => {\n                                if (!department.isDefaultDepartment) {\n                                  e.stopPropagation(); // Prevent event bubbling\n                                  setActiveDepartmentMenu(activeDepartmentMenu === department.id ? null : department.id);\n                                }\n                              }}\n                              disabled={department.isDefaultDepartment}\n                            >\n                              <ThreeDotsIcon />\n                            </Button>\n\n                            {activeDepartmentMenu === department.id && (\n                              <div className={styles.custom_dropdown}>\n                                <div\n                                  className={styles.dropdown_item}\n                                  onClick={() => {\n                                    openDepartmentModal(DEPARTMENT_ALTER_MODE.EDIT, department);\n                                    setActiveDepartmentMenu(null);\n                                  }}\n                                >\n                                  {t(\"edit\")}\n                                </div>\n                                <div className={styles.dropdown_divider}></div>\n                                <div\n                                  className={styles.dropdown_item}\n                                  onClick={() => {\n                                    openDepartmentModal(DEPARTMENT_ALTER_MODE.DELETE, department);\n                                    setActiveDepartmentMenu(null);\n                                  }}\n                                >\n                                  {t(\"delete\")}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                        <div\n                          className={styles.folder_container}\n                          onClick={() => {\n                            router.push(\n                              `${routes.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT_DETAIL}?departmentId=${department.id}&departmentName=${encodeURIComponent(department.name)}`\n                            );\n                          }}\n                          style={{ cursor: \"pointer\" }}\n                        >\n                          <FolderColorIcon className=\"folder-icon\" />\n                          <p className={styles.department_card} title={department.name}>\n                            {department.name}\n                          </p>\n                        </div>\n                      </li>\n                    ))\n                  )}\n                </ul>\n              )}\n            </div>\n          </div>\n        </div>\n      </section>\n      {/* \n      {roleModalConfig.show && (\n        <UserRoleModal\n          onClickCancel={() => setRoleModalConfig({ show: false, mode: EMPLOYEE_ALTER_MODE.EDIT, role: null })}\n          onSubmitSuccess={(message) => { \n            if (message) {\n              toastMessageSuccess(message);\n            } else {\n              toastMessageSuccess(`Role ${roleModalConfig.mode === EMPLOYEE_ALTER_MODE.ADD ? \"created\" : roleModalConfig.mode === EMPLOYEE_ALTER_MODE.EDIT ? \"updated\" : \"deleted\"} successfully`);\n            }\n          }}\n          role={roleModalConfig.role}\n          mode={roleModalConfig.mode}\n          disabled={isLoading}\n        />\n      )} */}\n\n      {departmentModalConfig.show && (\n        <DepartmentModal\n          onClickCancel={closeDepartmentModal}\n          onSubmitSuccess={(message) => {\n            fetchDepartments();\n            // Show toast notification if message is provided\n            if (message) {\n              toastMessageSuccess(message);\n            }\n          }}\n          department={departmentModalConfig.department}\n          mode={departmentModalConfig.mode}\n        />\n      )}\n    </>\n  );\n};\n\nexport default EmployeeManagement;\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;;AAqBO,MAAM,wBAAwB;IACnC,KAAK;IACL,MAAM;IACN,QAAQ;AACV;AAEA,MAAM,qBAAqB;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAAE,eAAe;YAAE,QAAQ;QAAG;IAAE;IACvF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IAExB,2DAA2D;IAC3D,mBAAmB;IACnB,0EAA0E;IAC1E,+CAA+C;IAC/C,mEAAmE;IAEnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAI9D;QAAE,MAAM;QAAO,MAAM,sBAAsB,GAAG;QAAE,YAAY;IAAK;IAEpE,MAAM,cAAc,MAAM;IAE1B,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,WAAW;YAC/B,iBAAiB;QACnB,GAAG,MAAM,4BAA4B;QAErC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAY;IAEhB,gCAAgC;IAChC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;YACvC,MAAM,SAAS,SAAS,IAAI;YAE5B,IAAI,UAAU,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBAC3C,qDAAqD;gBACrD,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG;oBAC9B,eAAe,OAAO,IAAI;gBAC5B,OAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,OAAO,OAAO,IAAI;oBACtC,eAAe,EAAE;gBACnB;YACF,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,OAAO,OAAO,IAAI;gBACtC,eAAe,EAAE;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,eAAe,EAAE;QACnB,SAAU;YACR,aAAa;QACf;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAC1B,MACA,aAA4C,IAAI;QAEhD,yBAAyB;YACvB,MAAM;YACN;YACA;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B,yBAAyB;YACvB,MAAM;YACN,MAAM,sBAAsB,GAAG;YAC/B,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,4DAA4D;YAC5D,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,wBAAwB;gBAC1C,wBAAwB;YAC1B;QACF;QAEA,yBAAyB;QACzB,SAAS,gBAAgB,CAAC,aAAa;QAEvC,WAAW;QACX,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IACL,qBACE;;0BACE,8OAAC;gBAAQ,WAAW,+JAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;wDAAG;sEACO,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;sDAIrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kJAAA,CAAA,UAAY;oDAAC,WAAU;8DACtB,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6IAAA,CAAA,UAAO;4DAAC,WAAU;4DAAqB,SAAS;4DAAS,MAAK;4DAAS,MAAK;4DAAO,aAAa,EAAE;sEACjG,cAAA,8OAAC,kJAAA,CAAA,UAAY,CAAC,IAAI;0EAChB,cAAA,8OAAC,iJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8DAMnB,8OAAC,4IAAA,CAAA,UAAM;oDAAC,WAAU;oDAAmC,SAAS,IAAM,oBAAoB,sBAAsB,GAAG;8DAC9G,EAAE;;;;;;;;;;;;;;;;;;gCAKR,0BACC,8OAAC;oCAAG,WAAU;8CACX,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,8OAAC;4CAAe,WAAU;sDACxB,cAAA,8OAAC,6JAAA,CAAA,UAAQ;gDAAC,QAAQ;gDAAK,OAAM;gDAAO,cAAc;gDAAI,QAAQ;;;;;;2CADvD;;;;;;;;;yDAMb,8OAAC;oCAAG,WAAU;8CACX,YAAY,MAAM,KAAK,kBACtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DACC,cAAA,8OAAC;8DAAQ,cAAc,EAAE,kCAAkC,EAAE;;;;;;;;;;;;;;;;;;;;+CAKnE,YAAY,GAAG,CAAC,CAAC,2BACf,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4IAAA,CAAA,UAAM;gEACL,WAAW,CAAC,cAAc,EAAE,WAAW,mBAAmB,GAAG,uBAAuB,IAAI;gEACxF,SAAS,CAAC;oEACR,IAAI,CAAC,WAAW,mBAAmB,EAAE;wEACnC,EAAE,eAAe,IAAI,yBAAyB;wEAC9C,wBAAwB,yBAAyB,WAAW,EAAE,GAAG,OAAO,WAAW,EAAE;oEACvF;gEACF;gEACA,UAAU,WAAW,mBAAmB;0EAExC,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;4DAGf,yBAAyB,WAAW,EAAE,kBACrC,8OAAC;gEAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,eAAe;;kFACpC,8OAAC;wEACC,WAAW,+JAAA,CAAA,UAAM,CAAC,aAAa;wEAC/B,SAAS;4EACP,oBAAoB,sBAAsB,IAAI,EAAE;4EAChD,wBAAwB;wEAC1B;kFAEC,EAAE;;;;;;kFAEL,8OAAC;wEAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,gBAAgB;;;;;;kFACvC,8OAAC;wEACC,WAAW,+JAAA,CAAA,UAAM,CAAC,aAAa;wEAC/B,SAAS;4EACP,oBAAoB,sBAAsB,MAAM,EAAE;4EAClD,wBAAwB;wEAC1B;kFAEC,EAAE;;;;;;;;;;;;;;;;;;;;;;;8DAMb,8OAAC;oDACC,WAAW,+JAAA,CAAA,UAAM,CAAC,gBAAgB;oDAClC,SAAS;wDACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,cAAc,CAAC,0BAA0B,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,WAAW,IAAI,GAAG;oDAE7I;oDACA,OAAO;wDAAE,QAAQ;oDAAU;;sEAE3B,8OAAC,sJAAA,CAAA,UAAe;4DAAC,WAAU;;;;;;sEAC3B,8OAAC;4DAAE,WAAW,+JAAA,CAAA,UAAM,CAAC,eAAe;4DAAE,OAAO,WAAW,IAAI;sEACzD,WAAW,IAAI;;;;;;;;;;;;;2CApDW,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAiF7D,sBAAsB,IAAI,kBACzB,8OAAC,qJAAA,CAAA,UAAe;gBACd,eAAe;gBACf,iBAAiB,CAAC;oBAChB;oBACA,iDAAiD;oBACjD,IAAI,SAAS;wBACX,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;oBACtB;gBACF;gBACA,YAAY,sBAAsB,UAAU;gBAC5C,MAAM,sBAAsB,IAAI;;;;;;;;AAK1C;uCAEe", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/DepartmentModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, useState, useEffect } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textbox from \"../formElements/Textbox\";\nimport { useForm } from \"react-hook-form\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { addDepartment, updateDepartment, deleteDepartment, IDepartmentAlter } from \"@/services/departmentService\";\nimport { departmentValidationSchema } from \"@/utils/validationSchema\";\n\nimport Loader from \"../loader/Loader\";\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { DEPARTMENT_ALTER_MODE } from \"../views/accessManagement/EmployeeManagement\";\nimport { useTranslations } from \"next-intl\";\nimport { DepartmentForm, DepartmentFormData, DepartmentModalProps } from \"@/interfaces/departmentInterface\";\nimport { normalizeSpaces } from \"@/utils/helper\";\n\nconst DepartmentModal: FC<DepartmentModalProps> = ({ onClickCancel, onSubmitSuccess, disabled, department, mode }) => {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitError, setSubmitError] = useState<string | null>(null);\n  const [isNameChanged, setIsNameChanged] = useState(false);\n  const t = useTranslations();\n\n  const {\n    control,\n    handleSubmit,\n    formState: { errors, isValid },\n    watch,\n  } = useForm<DepartmentFormData>({\n    defaultValues: {\n      name: department?.name || \"\",\n    },\n    resolver: yupResolver(departmentValidationSchema(t)),\n    mode: \"onChange\",\n  });\n\n  // Watch for changes in the name field\n  const currentName = watch(\"name\");\n\n  // Update isNameChanged when the name changes\n  useEffect(() => {\n    if (mode === DEPARTMENT_ALTER_MODE.EDIT && department) {\n      // Trim both values to ensure whitespace doesn't trigger unnecessary changes\n      const normalizedCurrentName = normalizeSpaces(currentName);\n      const normalizedOriginalName = normalizeSpaces(department.name);\n      setIsNameChanged(normalizedCurrentName !== normalizedOriginalName);\n    }\n  }, [currentName, department, mode]);\n\n  const onSubmit = async (data: DepartmentFormData) => {\n    try {\n      setIsSubmitting(true);\n      setSubmitError(null);\n\n      const requestData: DepartmentForm = {\n        name: normalizeSpaces(data.name),\n      };\n\n      let response: IApiResponseCommonInterface<IDepartmentAlter>;\n\n      try {\n        if (mode === DEPARTMENT_ALTER_MODE.ADD) {\n          response = await addDepartment(requestData);\n        } else if (mode === DEPARTMENT_ALTER_MODE.EDIT && department) {\n          response = await updateDepartment(department.id, requestData);\n        } else if (mode === DEPARTMENT_ALTER_MODE.DELETE && department) {\n          response = await deleteDepartment(department.id);\n        } else {\n          throw new Error(\"Invalid operation\");\n        }\n\n        const result = response.data;\n\n        if (result && result.success) {\n          // Call onSubmitSuccess with the success message\n          const successKey =\n            mode === DEPARTMENT_ALTER_MODE.ADD\n              ? \"department_added\"\n              : mode === DEPARTMENT_ALTER_MODE.EDIT\n                ? \"department_updated\"\n                : \"department_deleted\";\n          onSubmitSuccess(t(successKey));\n          onClickCancel();\n        } else {\n          setSubmitError(result.message);\n        }\n      } catch (error) {\n        const apiError = error as { response?: { status?: number; data?: { message?: string } } };\n        if (apiError.response?.status === 401) {\n          setSubmitError(t(\"department_auth_error\"));\n        } else {\n          setSubmitError(t(\"department_operation_failed\", { operation: mode }));\n        }\n      }\n    } catch (error) {\n      console.error(error);\n      setSubmitError(t(\"department_unexpected_error\"));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const getModalTitle = () => {\n    switch (mode) {\n      case DEPARTMENT_ALTER_MODE.ADD:\n        return t(\"department_add\");\n      case DEPARTMENT_ALTER_MODE.EDIT:\n        return t(\"department_update\");\n      case DEPARTMENT_ALTER_MODE.DELETE:\n        return t(\"department_delete\");\n      default:\n        return t(\"department_default\");\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center\">\n            <h2>{getModalTitle()}</h2>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel} disabled={isSubmitting}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body\">\n            {mode === DEPARTMENT_ALTER_MODE.DELETE ? (\n              <div>\n                <p className=\"text-center mb-4\">{t(\"department_delete_confirmation\", { name: department?.name || \"\" })}</p>\n\n                {submitError && (\n                  <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                    {submitError}\n                  </div>\n                )}\n\n                <div className=\"button-align mt-4\">\n                  <Button type=\"button\" className=\"danger-btn rounded-md w-100\" onClick={handleSubmit(onSubmit)} disabled={isSubmitting || disabled}>\n                    <div className=\"d-flex align-items-center justify-content-center\">\n                      {isSubmitting && <Loader />}\n                      <span className={isSubmitting ? \"ms-2\" : \"\"}>{getModalTitle()}</span>\n                    </div>\n                  </Button>\n                  <Button type=\"button\" className=\"dark-outline-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting || disabled}>\n                    {t(\"cancel\")}\n                  </Button>\n                </div>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit(onSubmit)}>\n                <InputWrapper className=\"mb-4\">\n                  <InputWrapper.Label htmlFor=\"name\" required className=\"fw-bold\">\n                    {t(\"department_name\")}\n                  </InputWrapper.Label>\n                  <Textbox\n                    className=\"form-control\"\n                    control={control}\n                    name=\"name\"\n                    type=\"text\"\n                    placeholder={t(\"department_name_placeholder\")}\n                    disabled={isSubmitting || disabled}\n                  />\n                  <InputWrapper.Error message={errors?.name?.message || \"\"} />\n                </InputWrapper>\n\n                {submitError && (\n                  <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                    {submitError}\n                  </div>\n                )}\n\n                <div className=\"button-align mt-4\">\n                  <Button\n                    type=\"submit\"\n                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || (mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged) || !isValid || normalizeSpaces(currentName) === \"\" ? \"truly-disabled\" : \"\"}`}\n                    disabled={\n                      isSubmitting ||\n                      disabled ||\n                      (mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged) ||\n                      !isValid ||\n                      normalizeSpaces(currentName) === \"\"\n                    }\n                    title={\n                      mode === DEPARTMENT_ALTER_MODE.EDIT && !isNameChanged\n                        ? t(\"department_name_change_hint\")\n                        : normalizeSpaces(currentName) === \"\"\n                          ? t(\"department_name_req\")\n                          : \"\"\n                    }\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center\">\n                      {isSubmitting && <Loader />}\n                      <span className={isSubmitting ? \"ms-2\" : \"\"}>{getModalTitle()}</span>\n                    </div>\n                  </Button>\n                  <Button type=\"button\" className=\"dark-outline-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting || disabled}>\n                    {t(\"cancel\")}\n                  </Button>\n                </div>\n              </form>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DepartmentModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AAhBA;;;;;;;;;;;;;;;AAkBA,MAAM,kBAA4C,CAAC,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE;IAC/G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC9B,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YACb,MAAM,YAAY,QAAQ;QAC5B;QACA,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,gIAAA,CAAA,6BAA0B,AAAD,EAAE;QACjD,MAAM;IACR;IAEA,sCAAsC;IACtC,MAAM,cAAc,MAAM;IAE1B,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,IAAI,YAAY;YACrD,4EAA4E;YAC5E,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;YAC9C,MAAM,yBAAyB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,IAAI;YAC9D,iBAAiB,0BAA0B;QAC7C;IACF,GAAG;QAAC;QAAa;QAAY;KAAK;IAElC,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAChB,eAAe;YAEf,MAAM,cAA8B;gBAClC,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;YACjC;YAEA,IAAI;YAEJ,IAAI;gBACF,IAAI,SAAS,qKAAA,CAAA,wBAAqB,CAAC,GAAG,EAAE;oBACtC,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,OAAO,IAAI,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,IAAI,YAAY;oBAC5D,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,EAAE,EAAE;gBACnD,OAAO,IAAI,SAAS,qKAAA,CAAA,wBAAqB,CAAC,MAAM,IAAI,YAAY;oBAC9D,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,EAAE;gBACjD,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,SAAS,SAAS,IAAI;gBAE5B,IAAI,UAAU,OAAO,OAAO,EAAE;oBAC5B,gDAAgD;oBAChD,MAAM,aACJ,SAAS,qKAAA,CAAA,wBAAqB,CAAC,GAAG,GAC9B,qBACA,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,GACjC,uBACA;oBACR,gBAAgB,EAAE;oBAClB;gBACF,OAAO;oBACL,eAAe,OAAO,OAAO;gBAC/B;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,WAAW;gBACjB,IAAI,SAAS,QAAQ,EAAE,WAAW,KAAK;oBACrC,eAAe,EAAE;gBACnB,OAAO;oBACL,eAAe,EAAE,+BAA+B;wBAAE,WAAW;oBAAK;gBACpE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,eAAe,EAAE;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IACA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK,qKAAA,CAAA,wBAAqB,CAAC,GAAG;gBAC5B,OAAO,EAAE;YACX,KAAK,qKAAA,CAAA,wBAAqB,CAAC,IAAI;gBAC7B,OAAO,EAAE;YACX,KAAK,qKAAA,CAAA,wBAAqB,CAAC,MAAM;gBAC/B,OAAO,EAAE;YACX;gBACE,OAAO,EAAE;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI;;;;;;0CACL,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAe,UAAU;0CACpE,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACZ,SAAS,qKAAA,CAAA,wBAAqB,CAAC,MAAM,iBACpC,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAoB,EAAE,kCAAkC;wCAAE,MAAM,YAAY,QAAQ;oCAAG;;;;;;gCAEnG,6BACC,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;8CAC3C;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAA8B,SAAS,aAAa;4CAAW,UAAU,gBAAgB;sDACvH,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,8BAAgB,8OAAC,sIAAA,CAAA,UAAM;;;;;kEACxB,8OAAC;wDAAK,WAAW,eAAe,SAAS;kEAAK;;;;;;;;;;;;;;;;;sDAGlD,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAoC,SAAS;4CAAe,UAAU,gBAAgB;sDACnH,EAAE;;;;;;;;;;;;;;;;;iDAKT,8OAAC;4BAAK,UAAU,aAAa;;8CAC3B,8OAAC,kJAAA,CAAA,UAAY;oCAAC,WAAU;;sDACtB,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAO,QAAQ;4CAAC,WAAU;sDACnD,EAAE;;;;;;sDAEL,8OAAC,6IAAA,CAAA,UAAO;4CACN,WAAU;4CACV,SAAS;4CACT,MAAK;4CACL,MAAK;4CACL,aAAa,EAAE;4CACf,UAAU,gBAAgB;;;;;;sDAE5B,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,QAAQ,MAAM,WAAW;;;;;;;;;;;;gCAGvD,6BACC,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;8CAC3C;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CACL,MAAK;4CACL,WAAW,CAAC,6BAA6B,EAAE,gBAAgB,YAAa,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,IAAI,CAAC,iBAAkB,CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB,KAAK,mBAAmB,IAAI;4CAC3M,UACE,gBACA,YACC,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,IAAI,CAAC,iBACzC,CAAC,WACD,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB;4CAEnC,OACE,SAAS,qKAAA,CAAA,wBAAqB,CAAC,IAAI,IAAI,CAAC,gBACpC,EAAE,iCACF,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB,KAC/B,EAAE,yBACF;sDAGR,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,8BAAgB,8OAAC,sIAAA,CAAA,UAAM;;;;;kEACxB,8OAAC;wDAAK,WAAW,eAAe,SAAS;kEAAK;;;;;;;;;;;;;;;;;sDAGlD,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAoC,SAAS;4CAAe,UAAU,gBAAgB;sDACnH,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;uCAEe", "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}