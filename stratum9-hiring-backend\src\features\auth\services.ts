/* eslint-disable camelcase */
/* eslint-disable no-async-promise-executor */
/* eslint-disable no-promise-executor-return */

import { HmacSHA256, enc } from "crypto-js";
import bcrypt from "bcryptjs";
import * as Sentry from "@sentry/node";

import {
  MESSAGE_TYPE,
  API_RESPONSE_MSG,
  PASSWORD_HASH_LENGTH,
  REDIS_EXPIRY,
  REDIS_KEYS,
} from "../../utils/constants";
import DbConnection from "../../db/dbConnection";

import { IForgotPassword, ILogin, IResetPassword, IVerify } from "./interface";

import createToken from "../../middleware/generate";
import { getSecretKeys } from "../../config/awsConfig";
import UserModel from "../../schema/s9/user";
import sendVerificationMail from "../../utils/verificationEmail";
import Employee from "../../schema/s9-innerview/employees";
import RolePermission from "../../schema/s9-innerview/role_permissions_mapping";
import Cache from "../../db/cache";
import OrganizationModel from "../../schema/s9/organization";
import SubscriptionServices from "../subscriptions/services";

export interface IPresignedData {
  filePath: string;
  fileFormat: string;
}

export class AuthServices {
  /**
   * find email.
   *
   * @param AuthParams
   */
  static getUserByEmail = (email: string) =>
    new Promise<UserModel>(async (resolve, reject) => {
      try {
        return DbConnection.getS9DataSource()
          .then(async (dataConnection) => {
            const userRepository = dataConnection.getRepository(UserModel);
            const user = await userRepository.findOne({ where: { email } });
            resolve(user);
            return user;
          })
          .catch((error) => {
            // Handle any other errors that occur during the database operation

            reject(error);
            return error;
          });
      } catch (e) {
        return e;
      }
    });

  /**
   * find user by id
   *
   * @param AuthParams
   */
  static getUserByUserId = async (userId: number) => {
    const dataConnection = await DbConnection.getS9DataSource();
    const repository = dataConnection.getRepository(UserModel);
    const user = await repository.findOne({
      where: { id: Number(userId) },
    });
    return user;
  };

  /**
   * Verify Otp
   * @param data
   * @returns
   */
  static verifyOtp = async (data: IVerify) => {
    try {
      const { email, otp } = data;

      if (!otp || !email) {
        return {
          success: false,
          message: API_RESPONSE_MSG.invalid_data,
        };
      }

      const userDetail = await AuthServices.getUserByEmail(email);

      if (!userDetail) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }

      const { verifyOtp } = userDetail;
      const keys = await getSecretKeys();
      const newOtpEncryption = enc.Base64.stringify(
        HmacSHA256(otp.toString(), keys.otp_enc_key)
      );

      if (newOtpEncryption !== verifyOtp.otp) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_otp,
        };
      }

      const currentTimeStamp = Date.now();
      const timeDifferenceMinutes =
        (currentTimeStamp - Number(verifyOtp.otp_created_ts)) / (1000 * 60);

      if (timeDifferenceMinutes > 5) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_expired,
        };
      }

      const dataConnection = await DbConnection.getS9DataSource();
      const repository = dataConnection.getRepository(UserModel);

      userDetail.isVerified = true;
      userDetail.updated_ts = new Date();
      await repository.save(userDetail);

      return {
        data: newOtpEncryption,
        success: true,
        message: API_RESPONSE_MSG.otp_verified,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Resend Otp
   * @param data
   * @returns
   */
  static resendOtp = async (data: IVerify) => {
    try {
      const { email } = data;

      const { success } = await this.sendVerificationEmail(email);

      if (!success) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_sending_failed,
        };
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.verification_code_sent,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * User login
   * @param data login object
   * @returns
   */

  static login = async (data: ILogin) => {
    try {
      const keys = await getSecretKeys();

      const { email, password } = data;
      console.log("email", email);
      console.log("password", password);
      // Query user details along with their address
      const userRepository =
        await DbConnection.getS9DatabaseRepository(UserModel);

      const userDetails = await userRepository.findOne({
        where: {
          email,
        },
        select: [
          "id",
          "email",
          "account_type",
          "password",
          "isVerified",
          "sms_notification",
          "allow_notification",
          "is_deleted",
          "image",
          "first_name",
          "last_name",
          "created_ts",
        ],
      });
      if (!userDetails || !userDetails.isVerified) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password, // to show message like "Email or password is incorrect"
        };
      }

      const employeeRepository =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);

      const employeeDetails = await employeeRepository
        .createQueryBuilder("employee")
        .leftJoinAndSelect("employee.role", "role")
        .leftJoinAndSelect("employee.department", "department")
        .where("employee.userId = :userId", { userId: userDetails.id })
        .select([
          "employee.id as employeeId",
          "employee.roleId as roleId",
          "employee.organizationId as organizationId",
          "employee.isActive as isActive",
          "employee.departmentId as departmentId",
          "role.id as roleId",
          "role.name as roleName",
          "role.isActive as roleIsActive",
          "department.id as departmentId",
          "department.name as departmentName",
          "department.isActive as departmentIsActive",
        ])
        .getRawOne();

      // Get organization details
      const organizationRepository =
        await DbConnection.getS9DatabaseRepository(OrganizationModel);
      const organizationDetails = await organizationRepository.findOne({
        where: {
          id: employeeDetails.organizationId,
        },
        select: ["id", "name", "organization_code"],
      });

      // get the current subscription plan for the organization with subscription and pricing details
      const orgSubscriptionInfo =
        await SubscriptionServices.getCurrentSubscription(
          employeeDetails.organizationId
        );

      console.log(">>>>>>>>>>>>>>>orgSubscriptionInfo", orgSubscriptionInfo);

      if (!employeeDetails || !employeeDetails.isActive) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password, // to show message like "Email or password is incorrect"
        };
      }

      const checkPassword = await bcrypt.compare(
        password.trim(),
        userDetails.password
      );

      if (!checkPassword) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password,
        };
      }

      const token = await createToken(
        {
          email: userDetails.email,
          id: userDetails.id,
          orgId: employeeDetails.organizationId,
          roleId: employeeDetails.roleId,
          departmentId: employeeDetails.departmentId,
          type: userDetails.account_type,
        },
        keys.token_key
      );

      delete userDetails.password;
      delete userDetails.fcm_tokens;

      const rolePermissionRepository =
        await DbConnection.getS9InnerViewDatabaseRepository(RolePermission);

      const permissions = await rolePermissionRepository
        .createQueryBuilder("rolePermission")
        .leftJoinAndSelect("rolePermission.permission", "permission")
        .where("rolePermission.roleId = :roleId", {
          roleId: employeeDetails.roleId,
        })
        .select(["permission.slug as permissionSlug"])
        .getRawMany();
      const permissionSlugs = permissions.map((perm) => perm.permissionSlug);

      const userInfo = {
        token,
        authData: {
          userData: {
            ...userDetails,
            orgId: employeeDetails.organizationId,
            organizationName: organizationDetails.name,
            organizationCode: organizationDetails.organization_code,
            createdTs: userDetails.created_ts.toISOString(),
          },
          department: {
            departmentId: employeeDetails.departmentId,
            departmentName: employeeDetails.departmentName,
            departmentIsActive: employeeDetails.departmentIsActive,
          },
          role: {
            roleId: employeeDetails.roleId,
            roleName: employeeDetails.roleName,
            roleIsActive: employeeDetails.roleIsActive,
          },
          permissions: permissionSlugs, // Now just an array of strings
          currentPlan: orgSubscriptionInfo,
        },
      };
      const cache = new Cache();
      const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
        "{userId}",
        String(userDetails?.id)
      );
      const permissionsKey = REDIS_KEYS.ROLE_PERMISSIONS.replace(
        "{roleId}",
        String(employeeDetails?.roleId)
      );

      // Store the session token
      await cache.lPush(sessionKey, token);

      // Store permission slugs in Redis
      // Delete any existing permissions first
      await cache.del(permissionsKey);

      // We've already extracted permissionSlugs above for the user response

      // Store permission slugs in Redis with DEFAULT_REDIS_EXPIRY from constants
      await cache.set(
        permissionsKey,
        JSON.stringify(permissionSlugs),
        REDIS_EXPIRY.DEFAULT
      );

      return {
        success: true,
        message: API_RESPONSE_MSG.login_successful,
        data: userInfo,
      };
    } catch (error) {
      console.log("Login Error", error);

      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.login_failed,
      };
    }
  };

  /**
   * Forgot Password
   * @param data - Object containing phone number
   * @returns An object with success status, message, and data (if applicable)
   */
  static forgotPassword = async (data: IForgotPassword) => {
    try {
      const { email } = data;

      return await this.sendVerificationEmail(email);
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Reset Password
   * @param data - Object containing new password and phone number
   * @returns An object with success status and message
   */
  static resetPassword = async (data: IResetPassword) => {
    try {
      const { password, email, otp } = data;

      const dataConnection = await DbConnection.getS9DataSource();
      const repository = dataConnection.getRepository(UserModel);

      const user = await AuthServices.getUserByEmail(email);

      if (!user) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }

      const { verifyOtp } = user;

      if (otp !== verifyOtp.otp) {
        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
        };
      }

      if (password) {
        const encPassword = await bcrypt.hash(password, PASSWORD_HASH_LENGTH);
        user.password = encPassword;
      }
      const currentTimeStamp = new Date().getTime();
      user.updated_ts = new Date();
      user.verifyOtp = {
        otp: null,
        otp_created_ts: currentTimeStamp.toString(),
      };

      await repository.save(user);

      return {
        success: true,
        message: API_RESPONSE_MSG.password_updated,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static deleteSession = async (token: string, userId: number) => {
    try {
      const cache = new Cache();
      const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
        "{userId}",
        String(userId)
      );

      await cache.lRem(sessionKey, token);

      return {
        success: true,
        message: API_RESPONSE_MSG.user_session_deleted,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateTimeZone = async (data: {
    timezone: string;
    userId: number;
  }) => {
    try {
      const { userId, timezone } = data;
      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);
      await userRepo
        .createQueryBuilder()
        .update(UserModel)
        .set({
          time_zone: timezone,
        })
        .where("id = :userId", { userId })
        .execute();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static sendVerificationEmail = async (email: string) => {
    try {
      const dataConnection = await DbConnection.getS9DataSource();
      const userRepository = dataConnection.getRepository(UserModel);
      const keys = await getSecretKeys();

      const currentTimeStamp = new Date().getTime();
      const userDetails = await this.getUserByEmail(email);

      if (!userDetails) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }

      const { first_name } = userDetails;

      const { otp, response } = await sendVerificationMail({
        email,
        firstname: first_name,
      });
      // Encrypt the OTP using a secret key
      if ((await response).message === MESSAGE_TYPE.SENT) {
        const encryptedOtp = enc.Base64.stringify(
          HmacSHA256(otp?.toString(), keys.otp_enc_key)
        );
        userDetails.verifyOtp = {
          otp: encryptedOtp,
          otp_created_ts: currentTimeStamp.toString(),
        };

        // Save the updated user details
        await userRepository.save(userDetails);

        return {
          success: true,
          message: API_RESPONSE_MSG.verification_code_sent,
        };
      }

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };
}

export default AuthServices;
