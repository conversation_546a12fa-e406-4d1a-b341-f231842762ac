import endpoint from "@/constants/endpoint";
import { http } from "@/utils/http";

import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import {
  PlanData,
  CreateCheckoutSessionRequest,
  ValidateSubscriptionDetailsRequest,
  CancelSubscriptionRequest,
  TransactionResponse,
} from "@/interfaces/subscriptionInterfaces";
import { ICurrentPlan } from "@/redux/slices/authSlice";

/**
 * Centralized subscription service that handles all subscription-related API calls
 */
export const subscriptionService = {
  /**
   * Get all available subscription plans
   */
  getAllPlans: (): Promise<IApiResponseCommonInterface<PlanData[]>> => {
    return http.get(endpoint.subscription.GET_ALL_PLANS);
  },

  /**
   * Get current subscription with detailed information
   */
  getCurrentSubscription: (): Promise<IApiResponseCommonInterface<ICurrentPlan>> => {
    return http.get(endpoint.subscription.GET_CURRENT_SUBSCRIPTION);
  },

  /**
   * Create a Stripe customer
   */
  createStripeCustomer: (): Promise<ApiResponse<null>> => {
    return http.post(endpoint.subscription.CREATE_STRIPE_CUSTOMER, {});
  },

  /**
   * Create a checkout session for subscription
   */
  createCheckoutSession: (data: CreateCheckoutSessionRequest): Promise<ApiResponse<null>> => {
    return http.post(endpoint.subscription.CREATE_CHECKOUT_SESSION, data);
  },

  /**
   * Validate subscription details before checkout
   */
  validateSubscriptionDetails: (data: ValidateSubscriptionDetailsRequest): Promise<ApiResponse<null>> => {
    return http.post(endpoint.subscription.VALIDATE_SUBSCRIPTION_DETAILS, data);
  },

  /**
   * Cancel subscription plan
   */
  cancelPlan: async (data: CancelSubscriptionRequest): Promise<ApiResponse> => {
    return http.post(endpoint.subscription.CANCEL_SUBSCRIPTION, data);
  },

  /**
   * Fetch subscription transactions history from the API
   * @param data Object with pagination parameters
   * @returns Promise resolving to TransactionResponse with pagination data
   */
  getTransactions: (data?: { limit?: number; offset?: number }): Promise<IApiResponseCommonInterface<TransactionResponse>> => {
    return http.get(endpoint.subscription.GET_TRANSACTIONS, { params: data });
  },
};
