import express from "express";
import HandleErrors from "../../middleware/handleError";
import { schemaValidation } from "../../middleware/validateSchema";
import {
  getCurrentSubscription,
  getAllPlans,
  cancelSubscription,
  createStripeCustomer,
  createCheckoutSession,
  validateSubscriptionDetails,
  getAllTransactions,
} from "./controller";
import {
  createCheckoutSessionValidation,
  validateSubscriptionDetailsValidation,
} from "./validation";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";

const subscriptionRoutes = express.Router();

/**
 * @swagger
 * /api/subscription/current:
 *   get:
 *     summary: Get Current Subscription
 *     description: Returns the current active subscription details for the authenticated organization.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current subscription retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Current subscription retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     planId:
 *                       type: number
 *                       example: 2
 *                     planName:
 *                       type: string
 *                       example: Professional Plan
 *                     subscriptionStatus:
 *                       type: string
 *                       enum: [active, canceled, past_due, unpaid]
 *                       example: active
 *                     expiryDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-07-01T00:00:00Z"
 *                     nextBillingDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-06-30T00:00:00Z"
 *                     pricingType:
 *                       type: string
 *                       enum: [monthly, yearly]
 *                       example: yearly
 *                     price:
 *                       type: number
 *                       example: 4999
 *                     isActive:
 *                       type: boolean
 *                       example: true
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: No active subscription found for this organization
 *       500:
 *         description: Server error while retrieving subscription details
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.CURRENT,
  auth,
  HandleErrors(getCurrentSubscription)
);

/**
 * @swagger
 * /api/subscription/all:
 *   get:
 *     summary: Get All Available Subscription Plans
 *     description: Returns a list of all available subscription plans with their features and pricing options.
 *     tags:
 *       - Subscription Routes
 *     responses:
 *       200:
 *         description: Available plans retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: All available plans retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       planId:
 *                         type: number
 *                         example: 2
 *                       planName:
 *                         type: string
 *                         example: Professional Plan
 *                       price:
 *                         type: number
 *                         example: 1999
 *                       pricingType:
 *                         type: string
 *                         enum: [monthly, yearly]
 *                         example: monthly
 *                       isActive:
 *                         type: boolean
 *                         example: true
 *                       description:
 *                         type: string
 *                         example: "Professional plan with advanced features"
 *                       features:
 *                         type: array
 *                         items:
 *                           type: string
 *                           example: "Unlimited assessments"
 *       500:
 *         description: Server error while retrieving plans
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.ALL_PLANS,
  auth,
  HandleErrors(getAllPlans)
);

/**
 * @swagger
 * /subscription/cancel:
 *   post:
 *     summary: Cancel Subscription
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *                 example: user_12345
 *             required:
 *               - user_id
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 code:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: Old plan successfully canceled. You will be upgraded to the new plan.
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.CANCEL,
  auth,
  // schemaValidation(cancelSubscriptionValidation),
  HandleErrors(cancelSubscription)
);

/**
 * @swagger
 * /api/stripe/create-customer:
 *   post:
 *     summary: Create Stripe Customer
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               name:
 *                 type: string
 *                 example: John Doe
 *             required:
 *               - email
 *               - name
 *     responses:
 *       201:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Customer created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     customer_id:
 *                       type: string
 *                       example: cus_abc123
 */
subscriptionRoutes.post(
  ROUTES.STRIPE.CREATE_CUSTOMER,
  auth,
  HandleErrors(createStripeCustomer)
);

/**
 * @swagger
 * /api/subscription/create-checkout-session:
 *   post:
 *     summary: Create Stripe Checkout Session
 *     description: Creates a new checkout session for subscribing to a plan. Handles both new subscriptions and upgrades from existing plans.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               planId:
 *                 type: number
 *                 description: The ID of the subscription plan
 *                 example: 2
 *               pricingId:
 *                 type: number
 *                 description: The ID of the pricing option (monthly/yearly)
 *                 example: 3
 *               successUrl:
 *                 type: string
 *                 description: URL to redirect after successful checkout
 *                 example: "https://app.s9innerview.com/account/subscription-success"
 *               cancelUrl:
 *                 type: string
 *                 description: URL to redirect if checkout is cancelled
 *                 example: "https://app.s9innerview.com/account/billing"
 *             required:
 *               - planId
 *               - pricingId
 *               - successUrl
 *               - cancelUrl
 *     responses:
 *       200:
 *         description: Checkout session created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Checkout session created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                       example: "cs_test_a1b2c3d4e5f6g7h8i9j0"
 *                     url:
 *                       type: string
 *                       example: "https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0"
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: Plan or pricing option not found
 *       500:
 *         description: Server error creating checkout session
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.CREATE_CHECKOUT_SESSION,
  auth,
  schemaValidation(createCheckoutSessionValidation),
  HandleErrors(createCheckoutSession)
);

/**
 * @swagger
 * /api/subscription/validate-details:
 *   post:
 *     summary: Validate subscription details before checkout
 *     description: Validates that the selected plan and pricing options exist and are active. Also checks if the organization has a Stripe customer record or creates one if needed.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               planId:
 *                 type: number
 *                 description: The ID of the subscription plan
 *                 example: 2
 *               pricingId:
 *                 type: number
 *                 description: The ID of the pricing option (monthly/yearly)
 *                 example: 3
 *             required:
 *               - planId
 *               - pricingId
 *     responses:
 *       200:
 *         description: Validation successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "All validations passed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     planId:
 *                       type: number
 *                       example: 2
 *                     planName:
 *                       type: string
 *                       example: "Professional Plan"
 *                     pricingId:
 *                       type: number
 *                       example: 3
 *                     pricingType:
 *                       type: string
 *                       example: "yearly"
 *                     price:
 *                       type: number
 *                       example: 4999
 *                     stripeCustomerId:
 *                       type: string
 *                       example: "cus_Nsd82AhdKJE9aj"
 *                     stripePriceId:
 *                       type: string
 *                       example: "price_1NqJH7JDEkdLsM4a8ZASFjdS"
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid or inactive plan ID"
 *                 data:
 *                   type: null
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Server error during validation
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.VALIDATE_DETAILS,
  auth,
  schemaValidation(validateSubscriptionDetailsValidation),
  HandleErrors(validateSubscriptionDetails)
);

/**
 * @swagger
 * /api/subscription/transactions:
 *   get:
 *     summary: Get Transaction Details
 *     description: Returns all transaction details for the authenticated user's organization
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Transaction details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Success
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: number
 *                         example: 123
 *                       payment_status:
 *                         type: string
 *                         enum: [Success, Pending, Failed]
 *                         example: Success
 *                       amount:
 *                         type: number
 *                         example: 49.99
 *                       transaction_type:
 *                         type: string
 *                         enum: [Purchase, Refund, Upgrade]
 *                         example: Purchase
 *                       transaction_method:
 *                         type: string
 *                         example: Card
 *                       transaction_date:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-07-15T10:30:00Z"
 *                       invoice_id:
 *                         type: string
 *                         example: "inv_123456789"
 *                       invoice_url:
 *                         type: string
 *                         example: "https://dashboard.stripe.com/invoices/inv_123456789"
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: No active subscription found for this organization
 *       500:
 *         description: Server error while retrieving transaction details
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.TRANSACTIONS,
  auth,
  HandleErrors(getAllTransactions)
);

export default subscriptionRoutes;
