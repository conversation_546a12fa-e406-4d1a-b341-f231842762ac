{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC,8JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/ReactCommonSelect.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { useState } from \"react\";\nimport { Controller, Control, FieldValues, Path, FieldErrors } from \"react-hook-form\";\nimport Select from \"react-select\";\n\n// Define option type\nexport interface SelectOption {\n  value: number;\n  label: string;\n  [key: string]: any;\n}\n\ninterface CommonSelectProps<T extends FieldValues = FieldValues> {\n  name: Path<T>;\n  control?: Control<T>;\n  options: readonly SelectOption[] | SelectOption[];\n  label?: string;\n  placeholder?: string;\n  isDisabled?: boolean;\n  isClearable?: boolean;\n  isSearchable?: boolean;\n  classNamePrefix?: string;\n  // value?: SelectOption | null;\n  onChange?: (value: SelectOption | null) => void;\n  onInputChange?: (value: string) => void;\n  errors?: FieldErrors<T>;\n  isLoading?: boolean;\n}\n\nconst ReactCommonSelect = <T extends FieldValues = FieldValues>({\n  name,\n  control,\n  options,\n  label,\n  placeholder,\n  isDisabled = false,\n  isClearable = true,\n  isSearchable = true,\n  classNamePrefix = \"select\",\n  // value,\n  onInputChange,\n  onChange,\n  errors,\n  isLoading,\n}: CommonSelectProps<T>) => {\n  const [isSelecting, setIsSelecting] = useState(false);\n  return (\n    <div className=\"form-group\">\n      {label && <label className=\"form-label\">{label}</label>}\n      <Controller\n        name={name}\n        control={control}\n        render={({ field }) => {\n          console.log(\"field\", field);\n          const selectedOption = field.value === 0 ? null : options.find((option) => option.value === field.value) || null;\n\n          // console.log(\"selectedOption\", selectedOption)\n          return (\n            <>\n              <Select\n                {...field}\n                className=\"form-control py-1 px-0\"\n                options={options}\n                placeholder={placeholder ? placeholder : \"\"}\n                isDisabled={isDisabled}\n                isClearable={isClearable}\n                isSearchable={isSearchable}\n                classNamePrefix={classNamePrefix}\n                value={selectedOption}\n                isLoading={isLoading}\n                onChange={(val) => {\n                  console.log(\"inside val\", val);\n                  setIsSelecting(true);\n                  if (onChange) {\n                    onChange(val);\n                  }\n                  field.onChange(val?.value);\n                  // Reset the flag after state updates\n                  setTimeout(() => setIsSelecting(false), 0);\n                }}\n                onInputChange={(inputValue, { action }) => {\n                  // if (onInputChange) {\n                  //   onInputChange(inputValue);\n                  // }\n                  // Only call onInputChange if it's an actual input change, not selection\n                  if (action === \"input-change\" && onInputChange && !isSelecting) {\n                    onInputChange(inputValue);\n                  }\n                }}\n                onMenuClose={() => {\n                  console.log(\"onMenuClose\");\n                }}\n                onBlur={field.onBlur}\n              />\n              {errors && errors[name] && <p className=\"text-danger auth-msg danger\">{errors[name]?.message as string}</p>}\n            </>\n          );\n        }}\n      />\n    </div>\n  );\n};\n\nexport default ReactCommonSelect;\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;;;;;AA0BA,MAAM,oBAAoB,CAAsC,EAC9D,IAAI,EACJ,OAAO,EACP,OAAO,EACP,KAAK,EACL,WAAW,EACX,aAAa,KAAK,EAClB,cAAc,IAAI,EAClB,eAAe,IAAI,EACnB,kBAAkB,QAAQ,EAC1B,SAAS;AACT,aAAa,EACb,QAAQ,EACR,MAAM,EACN,SAAS,EACY;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBAAS,8OAAC;gBAAM,WAAU;0BAAc;;;;;;0BACzC,8OAAC,8JAAA,CAAA,aAAU;gBACT,MAAM;gBACN,SAAS;gBACT,QAAQ,CAAC,EAAE,KAAK,EAAE;oBAChB,QAAQ,GAAG,CAAC,SAAS;oBACrB,MAAM,iBAAiB,MAAM,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;oBAE5G,gDAAgD;oBAChD,qBACE;;0CACE,8OAAC,iLAAA,CAAA,UAAM;gCACJ,GAAG,KAAK;gCACT,WAAU;gCACV,SAAS;gCACT,aAAa,cAAc,cAAc;gCACzC,YAAY;gCACZ,aAAa;gCACb,cAAc;gCACd,iBAAiB;gCACjB,OAAO;gCACP,WAAW;gCACX,UAAU,CAAC;oCACT,QAAQ,GAAG,CAAC,cAAc;oCAC1B,eAAe;oCACf,IAAI,UAAU;wCACZ,SAAS;oCACX;oCACA,MAAM,QAAQ,CAAC,KAAK;oCACpB,qCAAqC;oCACrC,WAAW,IAAM,eAAe,QAAQ;gCAC1C;gCACA,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE;oCACpC,uBAAuB;oCACvB,+BAA+B;oCAC/B,IAAI;oCACJ,wEAAwE;oCACxE,IAAI,WAAW,kBAAkB,iBAAiB,CAAC,aAAa;wCAC9D,cAAc;oCAChB;gCACF;gCACA,aAAa;oCACX,QAAQ,GAAG,CAAC;gCACd;gCACA,QAAQ,MAAM,MAAM;;;;;;4BAErB,UAAU,MAAM,CAAC,KAAK,kBAAI,8OAAC;gCAAE,WAAU;0CAA+B,MAAM,CAAC,KAAK,EAAE;;;;;;;;gBAG3F;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/CommonDatepicker.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport React from \"react\";\nimport { Control, Controller, FieldError, FieldErrors, FieldValues } from \"react-hook-form\";\nimport DatePicker from \"react-datepicker\";\nimport \"react-datepicker/dist/react-datepicker.css\";\n\ninterface IDateRange {\n  start: Date;\n  end: Date;\n}\n\ninterface IProps {\n  control: Control<any>;\n  name: string;\n  error?: FieldError | FieldErrors<FieldValues>;\n  className?: string;\n  label?: string;\n  dateFormat?: string;\n  onClick?: () => void;\n  dateRange: IDateRange[];\n  defaultDate?: Date;\n  placeholder?: string;\n  isClearable?: boolean;\n  icon?: string | undefined;\n  required?: boolean;\n}\n\nconst CommonDatePickerWrapper: React.FC<IProps> = ({\n  control,\n  name,\n  label,\n  dateRange,\n  defaultDate,\n  placeholder,\n  error,\n  required,\n  ...datePickerProps\n}) => {\n  return (\n    <div className={\"form-group common-datepicker-wrapper\"}>\n      <label>\n        {label && label} {required ? <sup>*</sup> : null}\n      </label>\n      <div className=\"form-control\">\n        <Controller\n          name={name}\n          control={control}\n          defaultValue={defaultDate && defaultDate}\n          render={({ field }) => (\n            <DatePicker\n              showIcon\n              disabledKeyboardNavigation\n              icon={\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" className=\"calendar-icon\">\n                  <path d=\"M152 24c0-13.3-10.7-24-24-24s-24 10.7-24 24V64H64C28.7 64 0 92.7 0 128v16 48V448c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V192 144 128c0-35.3-28.7-64-64-64H344V24c0-13.3-10.7-24-24-24s-24 10.7-24 24V64H152V24zM48 192H400V448c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192z\" />\n                </svg>\n              }\n              onFocus={(e) => e.target.blur()}\n              selected={field.value}\n              className={\"w-100 datepicker-input\"}\n              onChange={(date) => {\n                // Format date as YYYY-MM-DD\n                if (date) {\n                  const year = date.getFullYear();\n                  const month = String(date.getMonth() + 1).padStart(2, \"0\");\n                  const day = String(date.getDate()).padStart(2, \"0\");\n                  const formattedDate = `${year}-${month}-${day}`;\n\n                  // Update the form with formatted string instead of Date object\n                  field.onChange(formattedDate);\n                } else {\n                  field.onChange(date);\n                }\n              }}\n              excludeDateIntervals={dateRange}\n              minDate={new Date()}\n              placeholderText={placeholder}\n              {...datePickerProps}\n            />\n          )}\n        />\n      </div>\n      {error && <p className=\"auth-msg error m-0\">{error?.message as string}</p>}\n    </div>\n  );\n};\n\nexport default CommonDatePickerWrapper;\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AAErD;AACA;;;;;AAwBA,MAAM,0BAA4C,CAAC,EACjD,OAAO,EACP,IAAI,EACJ,KAAK,EACL,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAK,EACL,QAAQ,EACR,GAAG,iBACJ;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;;oBACE,SAAS;oBAAM;oBAAE,yBAAW,8OAAC;kCAAI;;;;;+BAAU;;;;;;;0BAE9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;oBACT,MAAM;oBACN,SAAS;oBACT,cAAc,eAAe;oBAC7B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,0JAAA,CAAA,UAAU;4BACT,QAAQ;4BACR,0BAA0B;4BAC1B,oBACE,8OAAC;gCAAI,OAAM;gCAA6B,SAAQ;gCAAc,WAAU;0CACtE,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;4BAGZ,SAAS,CAAC,IAAM,EAAE,MAAM,CAAC,IAAI;4BAC7B,UAAU,MAAM,KAAK;4BACrB,WAAW;4BACX,UAAU,CAAC;gCACT,4BAA4B;gCAC5B,IAAI,MAAM;oCACR,MAAM,OAAO,KAAK,WAAW;oCAC7B,MAAM,QAAQ,OAAO,KAAK,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;oCACtD,MAAM,MAAM,OAAO,KAAK,OAAO,IAAI,QAAQ,CAAC,GAAG;oCAC/C,MAAM,gBAAgB,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;oCAE/C,+DAA+D;oCAC/D,MAAM,QAAQ,CAAC;gCACjB,OAAO;oCACL,MAAM,QAAQ,CAAC;gCACjB;4BACF;4BACA,sBAAsB;4BACtB,SAAS,IAAI;4BACb,iBAAiB;4BAChB,GAAG,eAAe;;;;;;;;;;;;;;;;YAK1B,uBAAS,8OAAC;gBAAE,WAAU;0BAAsB,OAAO;;;;;;;;;;;;AAG1D;uCAEe", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/UploadDocumentIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype UploadDocumentIconProps = {\n  className?: string;\n};\n\nfunction UploadDocumentIcon({ className }: UploadDocumentIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"52\" height=\"52\" viewBox=\"0 0 52 52\" fill=\"none\" className={className}>\n      <g opacity=\"0.7\" clipPath=\"url(#clip0_9593_10462)\">\n        <path d=\"M32.5 17.332H32.5206\" stroke=\"#333333\" strokeWidth=\"2.6\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n        <path\n          d=\"M6.5 13C6.5 11.2761 7.18482 9.62279 8.40381 8.40381C9.62279 7.18482 11.2761 6.5 13 6.5H39C40.7239 6.5 42.3772 7.18482 43.5962 8.40381C44.8152 9.62279 45.5 11.2761 45.5 13V39C45.5 40.7239 44.8152 42.3772 43.5962 43.5962C42.3772 44.8152 40.7239 45.5 39 45.5H13C11.2761 45.5 9.62279 44.8152 8.40381 43.5962C7.18482 42.3772 6.5 40.7239 6.5 39V13Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M6.5 34.6673L17.3333 23.8339C19.344 21.8991 21.8227 21.8991 23.8333 23.8339L34.6667 34.6673\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M30.3281 30.3326L32.4948 28.166C34.5055 26.2311 36.9841 26.2311 38.9948 28.166L45.4948 34.666\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_10462\">\n          <rect width=\"52\" height=\"52\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default UploadDocumentIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAA2B;IAChE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAE,SAAQ;gBAAM,UAAS;;kCACxB,8OAAC;wBAAK,GAAE;wBAAuB,QAAO;wBAAU,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;kCACvG,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/UploadBox.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport UploadDocumentIcon from \"../svgComponents/UploadDocumentIcon\";\nimport { useTranslations } from \"next-intl\";\n\ninterface UploadBoxProps {\n  UploadBoxClassName?: string;\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  inputRef?: React.RefObject<HTMLInputElement | null>;\n  isLoading?: boolean;\n  uploadingMessages?: string[]; // Optional array of dynamic messages\n  messageInterval?: number; // Optional interval for message rotation (default: 2000ms)\n}\n\nconst UploadBox = ({ UploadBoxClassName, onChange, inputRef, isLoading, uploadingMessages, messageInterval = 2000 }: UploadBoxProps) => {\n  const t = useTranslations();\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n\n  // Dynamic message rotation effect\n  useEffect(() => {\n    if (!isLoading || !uploadingMessages || uploadingMessages.length <= 1) {\n      return;\n    }\n\n    const interval = setInterval(() => {\n      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % uploadingMessages.length);\n    }, messageInterval);\n\n    return () => clearInterval(interval);\n  }, [isLoading, uploadingMessages, messageInterval]);\n\n  // Reset message index when loading starts\n  useEffect(() => {\n    if (isLoading) {\n      setCurrentMessageIndex(0);\n    }\n  }, [isLoading]);\n\n  // Determine what message to show during loading\n  const getLoadingMessage = () => {\n    if (uploadingMessages && uploadingMessages.length > 0) {\n      return uploadingMessages[currentMessageIndex];\n    }\n    return t(\"uploading\");\n  };\n\n  return (\n    <div className={`upload-card ${UploadBoxClassName}`}>\n      <input type=\"file\" accept=\".pdf\" onChange={onChange} disabled={isLoading} ref={inputRef} />\n      <div className=\"upload-box-inner\">\n        <UploadDocumentIcon />\n        {!isLoading ? (\n          <p>\n            {t(\"upload_doc\")}\n            <br />\n            {t(\"max_file_size\")}\n          </p>\n        ) : (\n          <p className=\"uploading-message\">{getLoadingMessage()}</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UploadBox;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAWA,MAAM,YAAY,CAAC,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAkB;IACjI,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,qBAAqB,kBAAkB,MAAM,IAAI,GAAG;YACrE;QACF;QAEA,MAAM,WAAW,YAAY;YAC3B,uBAAuB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,kBAAkB,MAAM;QAClF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAW;QAAmB;KAAgB;IAElD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,uBAAuB;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;YACrD,OAAO,iBAAiB,CAAC,oBAAoB;QAC/C;QACA,OAAO,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,oBAAoB;;0BACjD,8OAAC;gBAAM,MAAK;gBAAO,QAAO;gBAAO,UAAU;gBAAU,UAAU;gBAAW,KAAK;;;;;;0BAC/E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yJAAA,CAAA,UAAkB;;;;;oBAClB,CAAC,0BACA,8OAAC;;4BACE,EAAE;0CACH,8OAAC;;;;;4BACA,EAAE;;;;;;6CAGL,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/UploadFileIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype UploadFileIconProps = {\n  className?: string;\n  onClick?: () => void;\n};\n\nfunction UploadFileIcon({ className, onClick }: UploadFileIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" className={className} onClick={onClick}>\n      <path\n        d=\"M27.9128 7.7745L20.413 0.274687C20.2379 0.0995625 19.999 0 19.75 0H6.625C5.07419 0 3.8125 1.26169 3.8125 2.8125V29.1875C3.8125 30.7383 5.07419 32 6.625 32H25.375C26.9258 32 28.1875 30.7383 28.1875 29.1875V8.4375C28.1875 8.18188 28.0802 7.94181 27.9128 7.7745ZM20.6875 3.20081L24.9867 7.5H21.625C21.1081 7.5 20.6875 7.07944 20.6875 6.5625V3.20081ZM25.375 30.125H6.625C6.10806 30.125 5.6875 29.7044 5.6875 29.1875V2.8125C5.6875 2.29556 6.10806 1.875 6.625 1.875H18.8125V6.5625C18.8125 8.11331 20.0742 9.375 21.625 9.375H26.3125V29.1875C26.3125 29.7044 25.8919 30.125 25.375 30.125Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 13.25H10.375C9.85725 13.25 9.4375 13.6697 9.4375 14.1875C9.4375 14.7053 9.85725 15.125 10.375 15.125H21.625C22.1427 15.125 22.5625 14.7053 22.5625 14.1875C22.5625 13.6697 22.1427 13.25 21.625 13.25Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 17H10.375C9.85725 17 9.4375 17.4197 9.4375 17.9375C9.4375 18.4553 9.85725 18.875 10.375 18.875H21.625C22.1427 18.875 22.5625 18.4553 22.5625 17.9375C22.5625 17.4197 22.1427 17 21.625 17Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M21.625 20.75H10.375C9.85725 20.75 9.4375 21.1697 9.4375 21.6875C9.4375 22.2053 9.85725 22.625 10.375 22.625H21.625C22.1427 22.625 22.5625 22.2053 22.5625 21.6875C22.5625 21.1697 22.1427 20.75 21.625 20.75Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M17.875 24.5H10.375C9.85725 24.5 9.4375 24.9197 9.4375 25.4375C9.4375 25.9553 9.85725 26.375 10.375 26.375H17.875C18.3927 26.375 18.8125 25.9553 18.8125 25.4375C18.8125 24.9197 18.3927 24.5 17.875 24.5Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default UploadFileIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,eAAe,EAAE,SAAS,EAAE,OAAO,EAAuB;IACjE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;QAAW,SAAS;;0BAC5H,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DeleteDarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype DeleteDarkIconProps = {\n  className?: string;\n  onClick?: () => void;\n};\n\nfunction DeleteDarkIcon({ className, onClick }: DeleteDarkIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"33\" height=\"32\" viewBox=\"0 0 33 32\" fill=\"none\" className={className} onClick={onClick}>\n      <g clip-path=\"url(#clip0_9593_10502)\">\n        <path d=\"M5.99951 9.33594H27.3328\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path d=\"M13.9995 14.6641V22.6641\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path d=\"M19.3335 14.6641V22.6641\" stroke=\"#333333\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" />\n        <path\n          d=\"M7.3335 9.33594L8.66683 25.3359C8.66683 26.0432 8.94778 26.7215 9.44788 27.2216C9.94797 27.7217 10.6263 28.0026 11.3335 28.0026H22.0002C22.7074 28.0026 23.3857 27.7217 23.8858 27.2216C24.3859 26.7215 24.6668 26.0432 24.6668 25.3359L26.0002 9.33594\"\n          stroke=\"#333333\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M12.6665 9.33333V5.33333C12.6665 4.97971 12.807 4.64057 13.057 4.39052C13.3071 4.14048 13.6462 4 13.9998 4H19.3332C19.6868 4 20.0259 4.14048 20.276 4.39052C20.526 4.64057 20.6665 4.97971 20.6665 5.33333V9.33333\"\n          stroke=\"#333333\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_10502\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(0.666504)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default DeleteDarkIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,eAAe,EAAE,SAAS,EAAE,OAAO,EAAuB;IACjE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;QAAW,SAAS;;0BAC5H,8OAAC;gBAAE,aAAU;;kCACX,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBAAK,GAAE;wBAA2B,QAAO;wBAAU,gBAAa;wBAAI,kBAAe;wBAAQ,mBAAgB;;;;;;kCAC5G,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Select.tsx"], "sourcesContent": ["import { HTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\nimport Loader from \"../loader/Loader\";\n\ninterface SelectProps<T extends FieldValues> extends HTMLAttributes<HTMLSelectElement> {\n  name: Path<T>;\n  placeholder?: string;\n  disabled?: boolean;\n  control: Control<T>;\n  options: Array<{ label: string; value: string | number }>;\n  isLoading?: boolean;\n}\n\nexport default function Select<T extends FieldValues>({ options, name, control, disabled, placeholder, isLoading, ...props }: SelectProps<T>) {\n  return (\n    <Controller\n      name={name}\n      control={control}\n      render={({ field }) => (\n        <select {...props} disabled={disabled} value={field.value} onChange={field.onChange} aria-label=\"\">\n          <option value=\"\">{placeholder}</option>\n          {isLoading ? (\n            <option value=\"0000\">\n              <Loader />\n            </option>\n          ) : (\n            options.map((data) => (\n              <option key={data.value} value={data.value}>\n                {data.label}\n              </option>\n            ))\n          )}\n        </select>\n      )}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAWe,SAAS,OAA8B,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAuB;IAC1I,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,MAAM;QACN,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gBAAQ,GAAG,KAAK;gBAAE,UAAU;gBAAU,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;kCAC9F,8OAAC;wBAAO,OAAM;kCAAI;;;;;;oBACjB,0BACC,8OAAC;wBAAO,OAAM;kCACZ,cAAA,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;iCAGT,QAAQ,GAAG,CAAC,CAAC,qBACX,8OAAC;4BAAwB,OAAO,KAAK,KAAK;sCACvC,KAAK,KAAK;2BADA,KAAK,KAAK;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/interviewServices.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\nimport endpoint from \"@/constants/endpoint\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  IAddInterviewSkillQuestion,\n  IGetCandidateListResponse,\n  IGetInterviewersResponse,\n  IGetInterviews,\n  IGetInterviewSkillQuestions,\n  IGetInterviewSkillQuestionsResponse,\n  IGetInterviewsResponse,\n  IGetJobListResponse,\n  IInterviewStaticInformation,\n  IScheduleInterview,\n  IUpcomingOrPastInterview,\n  IUpdateInterviewAnswers,\n  IUpdateInterviewSkillQuestion,\n  IUpdateScheduleInterview,\n} from \"@/interfaces/interviewInterfaces\";\n\nexport const updateOrScheduleInterview = (data: IScheduleInterview | IUpdateScheduleInterview): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);\n};\n\nexport const getInterviews = (data: IGetInterviews): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\n  return http.get(endpoint.interview.GET_INTERVIEWS, data);\n};\n\nexport const getInterviewers = (searchString: string, jobId: string): Promise<ApiResponse<IGetInterviewersResponse[]>> => {\n  return http.get(endpoint.interview.GET_INTERVIEWERS, { searchString, jobId });\n};\n\nexport const upcomigOrPastInterview = (params: {\n  isPast: boolean;\n  limit?: number;\n  offset?: number;\n  searchStr?: string;\n}): Promise<ApiResponse<IUpcomingOrPastInterview[]>> => {\n  return http.get(endpoint.interview.GET_UPCOMING_OR_PAST_INTERVIEW, { ...params });\n};\n\nexport const getMyInterviews = (monthYear: string): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\n  return http.get(endpoint.interview.GET_MY_INTERVIEWS, { monthYear });\n};\n\nexport const getInterviewSkillQuestions = (data: IGetInterviewSkillQuestions): Promise<ApiResponse<IGetInterviewSkillQuestionsResponse>> => {\n  return http.get(endpoint.interview.GET_INTERVIEW_SKILL_QUESTIONS, data);\n};\n\nexport const updateInterviewSkillQuestion = (data: IUpdateInterviewSkillQuestion): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);\n};\n\nexport const addInterviewSkillQuestion = (data: IAddInterviewSkillQuestion): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.ADD_INTERVIEW_SKILL_QUESTION, data);\n};\n\nexport const getJobList = (searchString: string): Promise<ApiResponse<IGetJobListResponse[]>> => {\n  return http.get(endpoint.interview.GET_JOB_LIST, { searchString });\n};\n\nexport const getCandidateList = (data: { searchString: string; jobId: string }): Promise<ApiResponse<IGetCandidateListResponse[]>> => {\n  return http.get(endpoint.interview.GET_CANDIDATE_LIST, data);\n};\n\nexport const updateInterviewAnswers = (data: IUpdateInterviewAnswers): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_ANSWERS, data);\n};\n\nexport const endInterview = (data: { interviewId: number; behaviouralNotes: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.END_INTERVIEW, data);\n};\n\nexport const conductInterviewStaticInformation = (): Promise<ApiResponse<IInterviewStaticInformation>> => {\n  return http.get(endpoint.interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAmBO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,cAAc,EAAE;AACrD;AAEO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE;QAAE;QAAc;IAAM;AAC7E;AAEO,MAAM,yBAAyB,CAAC;IAMrC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE;QAAE,GAAG,MAAM;IAAC;AACjF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE;QAAE;IAAU;AACpE;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE;AACpE;AAEO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,+BAA+B,EAAE;AACvE;AAEO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,YAAY,EAAE;QAAE;IAAa;AAClE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE;AACzD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE;AAChE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,aAAa,EAAE;AACrD;AAEO,MAAM,oCAAoC;IAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,oCAAoC;AACzE", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/CommonTimePicker.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport React from \"react\";\nimport { Control, Controller } from \"react-hook-form\";\nimport TimePicker from \"react-time-picker\";\nimport \"react-clock/dist/Clock.css\";\nimport \"react-time-picker/dist/TimePicker.css\";\n\ninterface CommonTimePickerProps {\n  name: string;\n  control: Control<any>;\n  label?: string;\n  required?: boolean;\n  defaultValue?: string;\n  disableClock?: boolean;\n  className?: string;\n  disabled?: boolean;\n}\n\nconst CommonTimePicker: React.FC<CommonTimePickerProps> = ({ name, control, defaultValue = \"00:00\", disableClock = true, disabled, className }) => {\n  return (\n    <Controller\n      name={name}\n      control={control}\n      defaultValue={defaultValue}\n      render={({ field }) => (\n        <TimePicker\n          {...field}\n          onChange={(value) => field.onChange(value)}\n          value={field.value}\n          disableClock={disableClock}\n          disabled={disabled}\n          className={className}\n          format=\"HH:mm\" // Ensures 24-hour format\n          clockIcon={null} // Optionally remove the clock icon for a cleaner look\n        />\n      )}\n    />\n  );\n};\n\nexport default CommonTimePicker;\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AAErD;AACA;;;;;;AAeA,MAAM,mBAAoD,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,OAAO,EAAE,eAAe,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE;IAC5I,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,MAAM;QACN,SAAS;QACT,cAAc;QACd,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+KAAA,CAAA,UAAU;gBACR,GAAG,KAAK;gBACT,UAAU,CAAC,QAAU,MAAM,QAAQ,CAAC;gBACpC,OAAO,MAAM,KAAK;gBAClB,cAAc;gBACd,UAAU;gBACV,WAAW;gBACX,QAAO,QAAQ,yBAAyB;;gBACxC,WAAW;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/CalendarEventModal.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-unused-expressions */\n/* eslint-disable @typescript-eslint/no-unused-expressions */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// src/components/commonModals/CalenderEventModal.tsx\n\"use client\";\nimport React, { FormEventHandler, useCallback, useEffect, useRef, useState } from \"react\";\nimport { Control, FieldErrors, UseFormGetValues, UseFormSetError, UseFormSetValue } from \"react-hook-form\";\nimport { debounce } from \"lodash\";\nimport toast from \"react-hot-toast\";\nimport { useTranslations } from \"next-intl\";\n\nimport \"../../styles/eventModal.scss\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textbox from \"../formElements/Textbox\";\n// import ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport Button from \"../formElements/Button\";\nimport {\n  IGetCandidateListResponse,\n  IGetInterviewsResponse,\n  IGetJobListResponse,\n  ScheduleInterviewFormValues,\n} from \"@/interfaces/interviewInterfaces\";\nimport ReactCommonSelect from \"../formElements/ReactCommonSelect\";\nimport CommonDatePickerWrapper from \"../formElements/CommonDatepicker\";\nimport Textarea from \"../formElements/Textarea\";\nimport { FILE_EXTENSION, INTERVIEW_SCHEDULE_ROUND_TYPE, ScheduleInterviewFormSubmissionType } from \"@/constants/commonConstants\";\nimport { toastMessageError, uploadFileOnS3 } from \"@/utils/helper\";\nimport UploadBox from \"../commonComponent/UploadBox\";\nimport UploadFileIcon from \"../svgComponents/UploadFileIcon\";\nimport DeleteDarkIcon from \"../svgComponents/DeleteDarkIcon\";\nimport Select from \"../formElements/Select\";\nimport { getCandidateList } from \"@/services/interviewServices\";\nimport CommonTimePicker from \"../formElements/CommonTimePicker\";\nimport { removeAttachmentsFromS3 } from \"@/services/commonService\";\n\ninterface EventModalProps {\n  onClose: () => void;\n  handleSubmit: FormEventHandler<HTMLFormElement>;\n  debouncedHandleSearchInputChange: (value: string) => void;\n  setFileUrls: React.Dispatch<React.SetStateAction<string[]>>;\n  fileUrls: string[];\n  control: Control<ScheduleInterviewFormValues | any>;\n  loading: boolean;\n  errors: FieldErrors<ScheduleInterviewFormValues>;\n  currentFileArrayLengthRef: React.RefObject<number>;\n  interviewers: Array<{ label: string; value: number }>;\n  loader: boolean;\n  formType: string;\n  getValues: UseFormGetValues<ScheduleInterviewFormValues>;\n  setValue: UseFormSetValue<ScheduleInterviewFormValues>;\n  setError: UseFormSetError<ScheduleInterviewFormValues>;\n  setJobs: React.Dispatch<React.SetStateAction<Array<IGetJobListResponse>>>;\n  interviewInfo: IGetInterviewsResponse | null;\n\n  debouncedHandleJobSearchInputChange?: (value: string) => void;\n  jobs?: Array<IGetJobListResponse>;\n  jobLoader?: boolean;\n  candidateName?: string;\n}\n\nconst CalendarEventModal: React.FC<EventModalProps> = ({\n  onClose,\n  control,\n  handleSubmit,\n  loading,\n  errors,\n  interviewers,\n  jobs,\n  loader,\n  debouncedHandleSearchInputChange,\n  debouncedHandleJobSearchInputChange,\n  setFileUrls,\n  getValues,\n  setValue,\n  setError,\n  fileUrls,\n  jobLoader,\n  formType,\n  currentFileArrayLengthRef,\n  interviewInfo,\n  setJobs,\n  candidateName,\n}) => {\n  const t = useTranslations();\n\n  const inputRef = useRef<HTMLInputElement>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [candidateLoading, setCandidateLoading] = useState(false);\n  const [candidates, setCandidates] = useState<IGetCandidateListResponse[]>([]);\n\n  console.log(\"fileUrls====\", fileUrls);\n\n  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    setIsLoading(true);\n    const { files } = e.target;\n    console.log(\"files\", files);\n    if (files?.length && Number(files?.[0].size) < 10854484) {\n      const file = files[0];\n      const uploadedUrls = [...(fileUrls || [])];\n\n      if (file) {\n        const Extension = file?.type?.split(\"/\")[1];\n        const fileNameArr = file.name.split(\".\");\n        const filePath = `job-interviews/${fileNameArr[0]}-${new Date().getTime()}.${fileNameArr[1]}`;\n        if (FILE_EXTENSION.includes(Extension?.toLowerCase())) {\n          const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;\n\n          uploadedUrls.push(uploadedFileUrl);\n          setFileUrls((prev) => [...(prev ?? []), uploadedFileUrl]);\n          currentFileArrayLengthRef.current++;\n        } else {\n          toastMessageError(t(\"invalid_file_format\"));\n        }\n      }\n    } else {\n      toastMessageError(t(\"invalid_size_format\"));\n    }\n    if (inputRef.current) {\n      inputRef.current.value = \"\";\n    }\n    setIsLoading(false);\n  };\n\n  const onModalClose = async () => {\n    if (inputRef.current) {\n      inputRef.current.value = \"\";\n    }\n    currentFileArrayLengthRef.current = 0;\n\n    onClose();\n    fileUrls?.length && (await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrls) }));\n    setFileUrls([]);\n  };\n\n  const onHandleDelete = async (fileUrl: string) => {\n    await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify(fileUrl) });\n    setFileUrls((prev) => prev && prev.filter((file) => file !== fileUrl));\n    currentFileArrayLengthRef.current--;\n  };\n\n  useEffect(() => {\n    if (formType === ScheduleInterviewFormSubmissionType.UPDATE && interviewInfo) {\n      const candidate = {\n        label: interviewInfo?.candidateName,\n        value: interviewInfo?.jobApplicationId,\n      };\n      setCandidates([candidate]);\n\n      setJobs([{ label: interviewInfo.jobTitle, value: interviewInfo.jobId, jobId: interviewInfo.jobUniqueId }]);\n    }\n    if (!!candidateName) {\n      setCandidates([{ label: candidateName, value: getValues(\"candidate\") }]);\n    }\n  }, []);\n\n  const getCandidates = useCallback(async (searchString: string, jobId?: number) => {\n    setCandidateLoading(true);\n    try {\n      const response = await getCandidateList({\n        searchString,\n        jobId: jobId?.toString() || \"\",\n      });\n\n      if (response?.data?.success) {\n        setCandidates(response?.data?.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching interviewers:\", error);\n    } finally {\n      setCandidateLoading(false);\n    }\n  }, []);\n\n  const handleCandidateSearchInputChange = (event: string) => {\n    const searchString = event.trim();\n    console.log(\"searchString\", searchString);\n    getCandidates(searchString, getValues(\"jobTitle\"));\n  };\n\n  const debouncedHandleCandidateSearchInputChange = debounce(handleCandidateSearchInputChange, 1000);\n\n  console.log(\"candidates\", candidates);\n\n  return (\n    <>\n      <div className=\"modal theme-modal show-modal modal-lg\">\n        <div className=\"modal-dialog modal-dialog-centered\">\n          <div className=\"modal-content\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"modal-header secondary-header\">\n                <h4 className=\"m-0\">{formType === ScheduleInterviewFormSubmissionType.UPDATE ? t(\"update_interview\") : t(\"schedule_interview\")}</h4>\n                <div className=\"button-align\">\n                  <Button type=\"submit\" disabled={loading} loading={loading} className=\"primary-btn rounded-md\">\n                    {formType === ScheduleInterviewFormSubmissionType.UPDATE ? t(\"update_interview\") : t(\"schedule_interview\")}\n                  </Button>\n                  <Button type=\"button\" onClick={onModalClose} disabled={loading} className=\"dark-outline-btn rounded-md\">\n                    {t(\"cancel\")}\n                  </Button>\n                </div>\n              </div>\n              <div className=\"modal-body\" style={{ height: \"calc(100vh - 350px)\", overflowY: \"auto\" }}>\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <ReactCommonSelect\n                      name=\"jobTitle\"\n                      control={control}\n                      options={jobs?.length ? jobs : []}\n                      isDisabled={formType === ScheduleInterviewFormSubmissionType.UPDATE || !!candidateName}\n                      label={t(\"job_title\")}\n                      placeholder={t(\"please_select_job\")}\n                      onInputChange={(e) => {\n                        if (debouncedHandleJobSearchInputChange) debouncedHandleJobSearchInputChange(e);\n                      }}\n                      onChange={(e) => {\n                        setValue(\"jobId\", e?.jobId);\n                        setValue(\"candidate\", -1);\n                        setError(\"jobId\", { type: \"manual\", message: \"\" });\n                        setCandidates([]);\n                        getCandidates(\"\", e?.value);\n                      }}\n                      // value={formType === ScheduleInterviewFormSubmissionType.UPDATE ? jobs?.[0] : null}\n                      isLoading={jobLoader}\n                      errors={errors}\n                    />\n                  </div>\n                  <div className=\"col-md-6\">\n                    <InputWrapper>\n                      <InputWrapper.Label htmlFor=\"jobId\" required>\n                        {t(\"job_id\")}\n                      </InputWrapper.Label>\n                      <Textbox className=\"form-control\" disabled control={control} name=\"jobId\" type=\"text\" placeholder={t(\"job_id_desc\")} />\n                      <InputWrapper.Error message={errors?.jobId?.message || \"\"} />\n                    </InputWrapper>\n                  </div>\n                </div>\n\n                <InputWrapper>\n                  <InputWrapper.Label htmlFor=\"eventTitle\" required>\n                    {t(\"title\")}\n                  </InputWrapper.Label>\n                  <Textbox className=\"form-control\" control={control} name=\"eventTitle\" type=\"text\" placeholder={t(\"title_desc\")} />\n                  <InputWrapper.Error message={errors?.eventTitle?.message || \"\"} />\n                </InputWrapper>\n\n                <ReactCommonSelect\n                  name=\"candidate\"\n                  control={control}\n                  options={candidates?.length ? candidates : []}\n                  isDisabled={formType === ScheduleInterviewFormSubmissionType.UPDATE || !!candidateName}\n                  label={t(\"candidate\")}\n                  // value={formType === ScheduleInterviewFormSubmissionType.UPDATE ? candidates[0] : null}\n                  placeholder={t(\"candidate_placeholder\")}\n                  onInputChange={(e) => debouncedHandleCandidateSearchInputChange(e)}\n                  isLoading={candidateLoading}\n                  errors={errors}\n                />\n\n                <ReactCommonSelect\n                  name=\"interviewer\"\n                  control={control}\n                  options={interviewers?.length ? interviewers : []}\n                  onChange={(val) => {\n                    if (!val) {\n                      console.log(\"val\", val);\n                      setValue(\"interviewer\", -1);\n                    }\n                  }}\n                  label={t(\"interviewer\")}\n                  placeholder={t(\"please_select_interviewer\")}\n                  onInputChange={(e) => debouncedHandleSearchInputChange(e)}\n                  isLoading={loader}\n                  errors={errors}\n                />\n\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <InputWrapper>\n                      <InputWrapper.Label htmlFor=\"interviewType\" required>\n                        {t(\"interview_type\")}\n                      </InputWrapper.Label>\n                      <Select\n                        options={INTERVIEW_SCHEDULE_ROUND_TYPE}\n                        className=\"form-control\"\n                        control={control}\n                        name=\"interviewType\"\n                        placeholder={t(\"please_select_interview_type\")}\n                      />\n                      <InputWrapper.Error message={errors?.interviewType?.message || \"\"} />\n                    </InputWrapper>\n                  </div>\n                  <div className=\"col-md-6\">\n                    <CommonDatePickerWrapper\n                      control={control}\n                      name=\"date\"\n                      label={t(\"interview_date\")}\n                      placeholder={t(\"please_select_date\")}\n                      error={errors?.date}\n                      dateRange={[]}\n                      required\n                      defaultDate={new Date()}\n                    />\n                  </div>\n                </div>\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <InputWrapper>\n                      <InputWrapper.Label htmlFor=\"startTime\" required>\n                        {t(\"start_time\")}\n                      </InputWrapper.Label>\n                      <CommonTimePicker name=\"startTime\" className=\"form-control\" control={control} />\n                      <InputWrapper.Error message={errors?.startTime?.message || \"\"} />\n                    </InputWrapper>\n                  </div>\n                  <div className=\"col-md-6\">\n                    <InputWrapper>\n                      <InputWrapper.Label htmlFor=\"endTime\" required>\n                        {t(\"end_time\")}\n                      </InputWrapper.Label>\n                      <CommonTimePicker name=\"endTime\" className=\"form-control\" control={control} />\n                      <InputWrapper.Error message={errors?.endTime?.message || \"\"} />\n                    </InputWrapper>\n                  </div>\n                  <InputWrapper>\n                    <InputWrapper.Label htmlFor=\"attachments\">{t(\"attachments\")}</InputWrapper.Label>\n                    <UploadBox\n                      UploadBoxClassName=\"upload-card-sm\"\n                      onChange={(e) => {\n                        if (currentFileArrayLengthRef.current >= 3) {\n                          toast.dismiss();\n                          toastMessageError(t(\"max_files_limit_msg\"));\n                        } else {\n                          onFileChange(e);\n                        }\n                      }}\n                      inputRef={inputRef}\n                      isLoading={isLoading}\n                    />\n                    {/* uploaded-item */}\n                    {fileUrls?.length\n                      ? fileUrls.map((file) => {\n                          const fileName = file?.split(\"/\").pop()?.split(\"-\")[1]?.split(\".\")[0];\n                          const fileExt = file?.split(\".\").pop();\n\n                          return (\n                            <div className=\"uploded-item upload-card-sm\" key={file}>\n                              <div className=\"item-name\">\n                                <UploadFileIcon />\n                                <p>\n                                  {fileName\n                                    ? fileName.length > 15\n                                      ? `${fileName.slice(0, 15)}...${fileName.slice(fileName.length - 3, fileName.length)}.${fileExt}`\n                                      : `${fileName}.${fileExt}`\n                                    : `document.${fileExt}`}\n                                </p>\n                              </div>\n                              <DeleteDarkIcon className=\"delete-item\" onClick={() => !isLoading && onHandleDelete(file)} />\n                            </div>\n                          );\n                        })\n                      : null}\n                  </InputWrapper>\n                  <InputWrapper>\n                    <InputWrapper.Label htmlFor=\"description\">{t(\"additional_info\")}</InputWrapper.Label>\n                    <Textarea className=\"form-control\" control={control} name=\"description\" rows={5} placeholder={t(\"additional_info_desc_\")} />\n                    <InputWrapper.Error message={errors?.description?.message || \"\"} />\n                  </InputWrapper>\n                </div>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default React.memo(CalendarEventModal);\n"], "names": [], "mappings": "AAAA,8CAA8C,GAC9C,wCAAwC,GACxC,2DAA2D,GAC3D,qDAAqD,GACrD,qDAAqD;;;;;AAErD;AAEA;AACA;AACA;AAGA;AACA;AACA,gEAAgE;AAChE;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7BA;;;;;;;;;;;;;;;;;;;;;;AAwDA,MAAM,qBAAgD,CAAC,EACrD,OAAO,EACP,OAAO,EACP,YAAY,EACZ,OAAO,EACP,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,MAAM,EACN,gCAAgC,EAChC,mCAAmC,EACnC,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,yBAAyB,EACzB,aAAa,EACb,OAAO,EACP,aAAa,EACd;IACC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B,EAAE;IAE5E,QAAQ,GAAG,CAAC,gBAAgB;IAE5B,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAC1B,QAAQ,GAAG,CAAC,SAAS;QACrB,IAAI,OAAO,UAAU,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,UAAU;YACvD,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,eAAe;mBAAK,YAAY,EAAE;aAAE;YAE1C,IAAI,MAAM;gBACR,MAAM,YAAY,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;gBAC3C,MAAM,cAAc,KAAK,IAAI,CAAC,KAAK,CAAC;gBACpC,MAAM,WAAW,CAAC,eAAe,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,OAAO,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,EAAE;gBAC7F,IAAI,mIAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,WAAW,gBAAgB;oBACrD,MAAM,kBAAmB,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;oBAEpD,aAAa,IAAI,CAAC;oBAClB,YAAY,CAAC,OAAS;+BAAK,QAAQ,EAAE;4BAAG;yBAAgB;oBACxD,0BAA0B,OAAO;gBACnC,OAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACtB;YACF;QACF,OAAO;YACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK,GAAG;QAC3B;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK,GAAG;QAC3B;QACA,0BAA0B,OAAO,GAAG;QAEpC;QACA,UAAU,UAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD,EAAE;YAAE,cAAc,KAAK,SAAS,CAAC;QAAU;QAC5F,YAAY,EAAE;IAChB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,MAAM,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD,EAAE;YAAE,cAAc,KAAK,SAAS,CAAC;QAAS;QACtE,YAAY,CAAC,OAAS,QAAQ,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS;QAC7D,0BAA0B,OAAO;IACnC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,mIAAA,CAAA,sCAAmC,CAAC,MAAM,IAAI,eAAe;YAC5E,MAAM,YAAY;gBAChB,OAAO,eAAe;gBACtB,OAAO,eAAe;YACxB;YACA,cAAc;gBAAC;aAAU;YAEzB,QAAQ;gBAAC;oBAAE,OAAO,cAAc,QAAQ;oBAAE,OAAO,cAAc,KAAK;oBAAE,OAAO,cAAc,WAAW;gBAAC;aAAE;QAC3G;QACA,IAAI,CAAC,CAAC,eAAe;YACnB,cAAc;gBAAC;oBAAE,OAAO;oBAAe,OAAO,UAAU;gBAAa;aAAE;QACzE;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,cAAsB;QAC7D,oBAAoB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACtC;gBACA,OAAO,OAAO,cAAc;YAC9B;YAEA,IAAI,UAAU,MAAM,SAAS;gBAC3B,cAAc,UAAU,MAAM;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,oBAAoB;QACtB;IACF,GAAG,EAAE;IAEL,MAAM,mCAAmC,CAAC;QACxC,MAAM,eAAe,MAAM,IAAI;QAC/B,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,cAAc,cAAc,UAAU;IACxC;IAEA,MAAM,4CAA4C,CAAA,GAAA,kIAAA,CAAA,UAAQ,AAAD,EAAE,kCAAkC;IAE7F,QAAQ,GAAG,CAAC,cAAc;IAE1B,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAO,aAAa,mIAAA,CAAA,sCAAmC,CAAC,MAAM,GAAG,EAAE,sBAAsB,EAAE;;;;;;kDACzG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4IAAA,CAAA,UAAM;gDAAC,MAAK;gDAAS,UAAU;gDAAS,SAAS;gDAAS,WAAU;0DAClE,aAAa,mIAAA,CAAA,sCAAmC,CAAC,MAAM,GAAG,EAAE,sBAAsB,EAAE;;;;;;0DAEvF,8OAAC,4IAAA,CAAA,UAAM;gDAAC,MAAK;gDAAS,SAAS;gDAAc,UAAU;gDAAS,WAAU;0DACvE,EAAE;;;;;;;;;;;;;;;;;;0CAIT,8OAAC;gCAAI,WAAU;gCAAa,OAAO;oCAAE,QAAQ;oCAAuB,WAAW;gCAAO;;kDACpF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,uJAAA,CAAA,UAAiB;oDAChB,MAAK;oDACL,SAAS;oDACT,SAAS,MAAM,SAAS,OAAO,EAAE;oDACjC,YAAY,aAAa,mIAAA,CAAA,sCAAmC,CAAC,MAAM,IAAI,CAAC,CAAC;oDACzE,OAAO,EAAE;oDACT,aAAa,EAAE;oDACf,eAAe,CAAC;wDACd,IAAI,qCAAqC,oCAAoC;oDAC/E;oDACA,UAAU,CAAC;wDACT,SAAS,SAAS,GAAG;wDACrB,SAAS,aAAa,CAAC;wDACvB,SAAS,SAAS;4DAAE,MAAM;4DAAU,SAAS;wDAAG;wDAChD,cAAc,EAAE;wDAChB,cAAc,IAAI,GAAG;oDACvB;oDACA,qFAAqF;oDACrF,WAAW;oDACX,QAAQ;;;;;;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAQ;4DAAQ,QAAQ;sEACzC,EAAE;;;;;;sEAEL,8OAAC,6IAAA,CAAA,UAAO;4DAAC,WAAU;4DAAe,QAAQ;4DAAC,SAAS;4DAAS,MAAK;4DAAQ,MAAK;4DAAO,aAAa,EAAE;;;;;;sEACrG,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAS,QAAQ,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAK7D,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAa,QAAQ;0DAC9C,EAAE;;;;;;0DAEL,8OAAC,6IAAA,CAAA,UAAO;gDAAC,WAAU;gDAAe,SAAS;gDAAS,MAAK;gDAAa,MAAK;gDAAO,aAAa,EAAE;;;;;;0DACjG,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,QAAQ,YAAY,WAAW;;;;;;;;;;;;kDAG9D,8OAAC,uJAAA,CAAA,UAAiB;wCAChB,MAAK;wCACL,SAAS;wCACT,SAAS,YAAY,SAAS,aAAa,EAAE;wCAC7C,YAAY,aAAa,mIAAA,CAAA,sCAAmC,CAAC,MAAM,IAAI,CAAC,CAAC;wCACzE,OAAO,EAAE;wCACT,yFAAyF;wCACzF,aAAa,EAAE;wCACf,eAAe,CAAC,IAAM,0CAA0C;wCAChE,WAAW;wCACX,QAAQ;;;;;;kDAGV,8OAAC,uJAAA,CAAA,UAAiB;wCAChB,MAAK;wCACL,SAAS;wCACT,SAAS,cAAc,SAAS,eAAe,EAAE;wCACjD,UAAU,CAAC;4CACT,IAAI,CAAC,KAAK;gDACR,QAAQ,GAAG,CAAC,OAAO;gDACnB,SAAS,eAAe,CAAC;4CAC3B;wCACF;wCACA,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,eAAe,CAAC,IAAM,iCAAiC;wCACvD,WAAW;wCACX,QAAQ;;;;;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAQ;4DAAgB,QAAQ;sEACjD,EAAE;;;;;;sEAEL,8OAAC,4IAAA,CAAA,UAAM;4DACL,SAAS,mIAAA,CAAA,gCAA6B;4DACtC,WAAU;4DACV,SAAS;4DACT,MAAK;4DACL,aAAa,EAAE;;;;;;sEAEjB,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAS,QAAQ,eAAe,WAAW;;;;;;;;;;;;;;;;;0DAGnE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sJAAA,CAAA,UAAuB;oDACtB,SAAS;oDACT,MAAK;oDACL,OAAO,EAAE;oDACT,aAAa,EAAE;oDACf,OAAO,QAAQ;oDACf,WAAW,EAAE;oDACb,QAAQ;oDACR,aAAa,IAAI;;;;;;;;;;;;;;;;;kDAIvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAQ;4DAAY,QAAQ;sEAC7C,EAAE;;;;;;sEAEL,8OAAC,sJAAA,CAAA,UAAgB;4DAAC,MAAK;4DAAY,WAAU;4DAAe,SAAS;;;;;;sEACrE,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAS,QAAQ,WAAW,WAAW;;;;;;;;;;;;;;;;;0DAG/D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAQ;4DAAU,QAAQ;sEAC3C,EAAE;;;;;;sEAEL,8OAAC,sJAAA,CAAA,UAAgB;4DAAC,MAAK;4DAAU,WAAU;4DAAe,SAAS;;;;;;sEACnE,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4DAAC,SAAS,QAAQ,SAAS,WAAW;;;;;;;;;;;;;;;;;0DAG7D,8OAAC,kJAAA,CAAA,UAAY;;kEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wDAAC,SAAQ;kEAAe,EAAE;;;;;;kEAC7C,8OAAC,kJAAA,CAAA,UAAS;wDACR,oBAAmB;wDACnB,UAAU,CAAC;4DACT,IAAI,0BAA0B,OAAO,IAAI,GAAG;gEAC1C,uJAAA,CAAA,UAAK,CAAC,OAAO;gEACb,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;4DACtB,OAAO;gEACL,aAAa;4DACf;wDACF;wDACA,UAAU;wDACV,WAAW;;;;;;oDAGZ,UAAU,SACP,SAAS,GAAG,CAAC,CAAC;wDACZ,MAAM,WAAW,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE;wDACrE,MAAM,UAAU,MAAM,MAAM,KAAK;wDAEjC,qBACE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,qJAAA,CAAA,UAAc;;;;;sFACf,8OAAC;sFACE,WACG,SAAS,MAAM,GAAG,KAChB,GAAG,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,SAAS,KAAK,CAAC,SAAS,MAAM,GAAG,GAAG,SAAS,MAAM,EAAE,CAAC,EAAE,SAAS,GAC/F,GAAG,SAAS,CAAC,EAAE,SAAS,GAC1B,CAAC,SAAS,EAAE,SAAS;;;;;;;;;;;;8EAG7B,8OAAC,qJAAA,CAAA,UAAc;oEAAC,WAAU;oEAAc,SAAS,IAAM,CAAC,aAAa,eAAe;;;;;;;2DAXpC;;;;;oDActD,KACA;;;;;;;0DAEN,8OAAC,kJAAA,CAAA,UAAY;;kEACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wDAAC,SAAQ;kEAAe,EAAE;;;;;;kEAC7C,8OAAC,8IAAA,CAAA,UAAQ;wDAAC,WAAU;wDAAe,SAAS;wDAAS,MAAK;wDAAc,MAAM;wDAAG,aAAa,EAAE;;;;;;kEAChG,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wDAAC,SAAS,QAAQ,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjF;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/conductInterview.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additional\": \"conductInterview-module-scss-module__yztraq__additional\",\n  \"completed\": \"conductInterview-module-scss-module__yztraq__completed\",\n  \"conduct_interview\": \"conductInterview-module-scss-module__yztraq__conduct_interview\",\n  \"conduct_interview_page\": \"conductInterview-module-scss-module__yztraq__conduct_interview_page\",\n  \"current\": \"conductInterview-module-scss-module__yztraq__current\",\n  \"question_info_box\": \"conductInterview-module-scss-module__yztraq__question_info_box\",\n  \"summary_card_height\": \"conductInterview-module-scss-module__yztraq__summary_card_height\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/interviewValidations.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\nexport const scheduleInterviewValidation = (translation: (key: string) => string) =>\n  yup.object({\n    eventTitle: yup.string().required(translation(\"title_req\")).min(5, translation(\"title_min_5_chars\")).max(100, translation(\"title_max_100_chars\")),\n    jobTitle: yup.number().required(translation(\"job_title_req\")),\n    interviewType: yup.string().required(translation(\"interview_type_req\")),\n    jobId: yup.string().required(translation(\"job_id_req\")),\n    date: yup\n      .string()\n      .required(translation(\"date_req\"))\n      .test(\"is-not-in-past\", translation(\"cannot_schedule_interview_past\"), function (dateStr) {\n        if (!dateStr) return true;\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const inputDate = new Date(dateStr);\n        inputDate.setHours(0, 0, 0, 0);\n        return inputDate >= today;\n      })\n      .test(\"not-more-than-month-in-advance\", translation(\"cannot_schedule_more_than_one_month_in_advance\"), function (dateStr) {\n        if (!dateStr) return true;\n        const oneMonthFromNow = new Date();\n        oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);\n        oneMonthFromNow.setHours(0, 0, 0, 0);\n        const inputDate = new Date(dateStr);\n        inputDate.setHours(0, 0, 0, 0);\n        return inputDate <= oneMonthFromNow;\n      }),\n\n    startTime: yup\n      .string()\n      .required(translation(\"start_time_req\"))\n      .matches(/^([01]\\d|2[0-3]):([0-5]\\d)$/, translation(\"invalid_time_format\")),\n    description: yup.string().nullable().optional().max(2000, translation(\"description_max_2000_chars\")),\n    endTime: yup\n      .string()\n      .required(translation(\"end_time_req\"))\n      .matches(/^([01]\\d|2[0-3]):([0-5]\\d)$/, translation(\"invalid_time_format\"))\n      .test(\"is-greater-than-start-time\", translation(\"end_time_must_be_after_start_time\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Compare times\n        return endTime > startTime;\n      })\n      .test(\"is-at-least-30-min\", translation(\"interview_must_be_at_least_10_min\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Parse hours and minutes\n        const [startHour, startMin] = startTime.split(\":\").map(Number);\n        const [endHour, endMin] = endTime.split(\":\").map(Number);\n\n        // Calculate total minutes\n        const startTotalMins = startHour * 60 + startMin;\n        const endTotalMins = endHour * 60 + endMin;\n        const diffMins = endTotalMins - startTotalMins;\n\n        // Ensure at least 30 minutes difference\n        return diffMins >= 30;\n      })\n      .test(\"max-duration\", translation(\"interview_must_not_exceed_2_hours\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Parse hours and minutes\n        const [startHour, startMin] = startTime.split(\":\").map(Number);\n        const [endHour, endMin] = endTime.split(\":\").map(Number);\n\n        // Calculate total minutes\n        const startTotalMins = startHour * 60 + startMin;\n        const endTotalMins = endHour * 60 + endMin;\n        const diffMins = endTotalMins - startTotalMins;\n\n        // Ensure interview is no longer than 2 hours (120 minutes)\n        return diffMins <= 120;\n      }),\n    interviewer: yup.number().required(translation(\"interviewer_req\")),\n    candidate: yup\n      .number()\n      .test(\"job-title-required\", translation(\"please_select_job_title_first\"), function () {\n        const { jobTitle } = this.parent;\n        return !!jobTitle && jobTitle > 0;\n      })\n      .required(translation(\"candidate_req\")),\n  });\n\nexport const addUpdateQuestionValidation = (translation: (key: string) => string) =>\n  yup.object().shape({\n    question: yup.string().required(translation(\"question_req\")).max(500, translation(\"question_max_500_chars\")),\n  });\n\nexport const addAnswerValidation = (translation: (key: string) => string) =>\n  yup\n    .object()\n    .shape({\n      behavioralInfo: yup.string().optional().max(1500, translation(\"behavioral_info_max_1500_chars\")),\n    })\n    .test(\"dynamic-answer-validation\", \"\", function (values) {\n      console.log(\"values========\", values);\n      // Get all field names from the form values\n      const fieldNames = Object.keys(values || {});\n\n      // Create a new schema object to add dynamic validations\n      let dynamicSchema = yup.object().shape({});\n\n      // Add validation for each answer field\n      fieldNames.forEach((fieldName) => {\n        if (fieldName.startsWith(\"answer-\")) {\n          // Add validation for this specific answer field\n          dynamicSchema = dynamicSchema.shape({\n            [fieldName]: yup.string().optional().max(2000, translation(\"answer_max_2000_chars\")),\n          });\n        }\n      });\n\n      try {\n        dynamicSchema.validateSync(values, { abortEarly: false });\n        return true;\n      } catch (error) {\n        console.log(\"error\", error);\n        return false;\n      }\n    });\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,MAAM,8BAA8B,CAAC,cAC1C,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,EAAE;QACT,YAAY,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY,cAAc,GAAG,CAAC,GAAG,YAAY,sBAAsB,GAAG,CAAC,KAAK,YAAY;QAC1H,UAAU,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QAC5C,eAAe,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QACjD,OAAO,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QACzC,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,QAAQ,CAAC,YAAY,aACrB,IAAI,CAAC,kBAAkB,YAAY,mCAAmC,SAAU,OAAO;YACtF,IAAI,CAAC,SAAS,OAAO;YACrB,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YACxB,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,OAAO,aAAa;QACtB,GACC,IAAI,CAAC,kCAAkC,YAAY,mDAAmD,SAAU,OAAO;YACtH,IAAI,CAAC,SAAS,OAAO;YACrB,MAAM,kBAAkB,IAAI;YAC5B,gBAAgB,QAAQ,CAAC,gBAAgB,QAAQ,KAAK;YACtD,gBAAgB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAClC,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,OAAO,aAAa;QACtB;QAEF,WAAW,CAAA,GAAA,mIAAA,CAAA,SACF,AAAD,IACL,QAAQ,CAAC,YAAY,mBACrB,OAAO,CAAC,+BAA+B,YAAY;QACtD,aAAa,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;QACtE,SAAS,CAAA,GAAA,mIAAA,CAAA,SACA,AAAD,IACL,QAAQ,CAAC,YAAY,iBACrB,OAAO,CAAC,+BAA+B,YAAY,wBACnD,IAAI,CAAC,8BAA8B,YAAY,sCAAsC,SAAU,OAAO;YACrG,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,gBAAgB;YAChB,OAAO,UAAU;QACnB,GACC,IAAI,CAAC,sBAAsB,YAAY,sCAAsC,SAAU,OAAO;YAC7F,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,0BAA0B;YAC1B,MAAM,CAAC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YACvD,MAAM,CAAC,SAAS,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEjD,0BAA0B;YAC1B,MAAM,iBAAiB,YAAY,KAAK;YACxC,MAAM,eAAe,UAAU,KAAK;YACpC,MAAM,WAAW,eAAe;YAEhC,wCAAwC;YACxC,OAAO,YAAY;QACrB,GACC,IAAI,CAAC,gBAAgB,YAAY,sCAAsC,SAAU,OAAO;YACvF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,0BAA0B;YAC1B,MAAM,CAAC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YACvD,MAAM,CAAC,SAAS,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEjD,0BAA0B;YAC1B,MAAM,iBAAiB,YAAY,KAAK;YACxC,MAAM,eAAe,UAAU,KAAK;YACpC,MAAM,WAAW,eAAe;YAEhC,2DAA2D;YAC3D,OAAO,YAAY;QACrB;QACF,aAAa,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QAC/C,WAAW,CAAA,GAAA,mIAAA,CAAA,SACF,AAAD,IACL,IAAI,CAAC,sBAAsB,YAAY,kCAAkC;YACxE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM;YAChC,OAAO,CAAC,CAAC,YAAY,WAAW;QAClC,GACC,QAAQ,CAAC,YAAY;IAC1B;AAEK,MAAM,8BAA8B,CAAC,cAC1C,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,UAAU,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY,iBAAiB,GAAG,CAAC,KAAK,YAAY;IACpF;AAEK,MAAM,sBAAsB,CAAC,cAClC,CAAA,GAAA,mIAAA,CAAA,SACS,AAAD,IACL,KAAK,CAAC;QACL,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;IAChE,GACC,IAAI,CAAC,6BAA6B,IAAI,SAAU,MAAM;QACrD,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,2CAA2C;QAC3C,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,CAAC;QAE1C,wDAAwD;QACxD,IAAI,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC,CAAC;QAExC,uCAAuC;QACvC,WAAW,OAAO,CAAC,CAAC;YAClB,IAAI,UAAU,UAAU,CAAC,YAAY;gBACnC,gDAAgD;gBAChD,gBAAgB,cAAc,KAAK,CAAC;oBAClC,CAAC,UAAU,EAAE,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;gBAC7D;YACF;QACF;QAEA,IAAI;YACF,cAAc,YAAY,CAAC,QAAQ;gBAAE,YAAY;YAAM;YACvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;YACrB,OAAO;QACT;IACF", "debugId": null}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1788, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/EditIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype EditIconProps = {\n  className?: string;\n  fillNone?: boolean;\n  fillColor?: string;\n};\n\nfunction EditIcon({ className, fillNone, fillColor }: EditIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 27 26\" fill=\"none\" className={className}>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539Z\"\n        fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n      />\n      <mask id=\"mask0_11116_355\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"24\" height=\"26\">\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M0.666992 0H23.5744V25.2527H0.666992V0Z\" fill=\"white\" />\n      </mask>\n      <g mask=\"url(#mask0_11116_355)\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z\"\n          fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n        />\n      </g>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.6316 11.5792C19.4182 11.5792 19.2049 11.5112 19.0222 11.3725L11.7529 5.78851C11.3156 5.45251 11.2329 4.82584 11.5689 4.38584C11.9062 3.94851 12.5329 3.86717 12.9716 4.20317L20.2422 9.78584C20.6796 10.1218 20.7622 10.7498 20.4249 11.1885C20.2289 11.4445 19.9316 11.5792 19.6316 11.5792Z\"\n        fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n      />\n    </svg>\n  );\n}\n\nexport default EditIcon;\n"], "names": [], "mappings": ";;;;;AAQA,SAAS,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAiB;IACjE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;0BAExD,8OAAC;gBAAK,IAAG;gBAAkB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;0BACpH,cAAA,8OAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;oBAA0C,MAAK;;;;;;;;;;;0BAE/F,8OAAC;gBAAE,MAAK;0BACN,cAAA,8OAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;;;;;;0BAG1D,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;;;;;;;AAI9D;uCAEe", "debugId": null}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1925, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/JoinMeetingIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction JoinMeetingIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"28\" height=\"21\" viewBox=\"0 0 28 21\" fill=\"none\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M19.7289 15.2171C19.8367 17.6599 17.8652 19.7255 15.3258 19.8293C15.1388 19.8372 6.02009 19.8188 6.02009 19.8188C3.49296 20.0106 1.28121 18.1947 1.08188 15.7611C1.06686 15.5798 1.07095 5.7956 1.07095 5.7956C0.959001 3.35022 2.92773 1.27933 5.46852 1.17158C5.65829 1.16239 14.7647 1.17947 14.7647 1.17947C17.3041 0.99025 19.5227 2.81936 19.7193 5.26474C19.7329 5.44082 19.7289 15.2171 19.7289 15.2171Z\"\n        stroke=\"white\"\n        stroke-width=\"2\"\n        stroke-linecap=\"round\"\n        stroke-linejoin=\"round\"\n      />\n      <path\n        d=\"M19.7334 7.8071L24.1241 4.21377C25.2121 3.3231 26.8441 4.0991 26.8427 5.5031L26.8267 15.3018C26.8254 16.7058 25.1921 17.4751 24.1067 16.5844L19.7334 12.9911\"\n        stroke=\"white\"\n        stroke-width=\"2\"\n        stroke-linecap=\"round\"\n        stroke-linejoin=\"round\"\n      />\n    </svg>\n  );\n}\n\nexport default JoinMeetingIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IAC5D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,aAAU;gBACV,aAAU;gBACV,GAAE;gBACF,QAAO;gBACP,gBAAa;gBACb,kBAAe;gBACf,mBAAgB;;;;;;0BAElB,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,gBAAa;gBACb,kBAAe;gBACf,mBAAgB;;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 1971, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1977, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/CopyLinkDarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction CopyLinkDarkIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"28\" height=\"21\" viewBox=\"0 0 33 33\" fill=\"none\">\n      <g clip-path=\"url(#clip0_12832_8815)\">\n        <path\n          d=\"M4.37051 20.4559C3.27676 19.3621 2.73301 18.0371 2.73301 16.4934C2.73301 15.0121 3.32676 13.5934 4.37051 12.5434C5.46426 11.4496 6.78301 10.9059 8.33301 10.9059H13.933C14.5955 10.9059 15.133 10.3684 15.133 9.70586C15.133 9.04336 14.5955 8.50586 13.933 8.50586H8.33301C6.12051 8.50586 4.23301 9.28711 2.67051 10.8434C1.10801 12.3996 0.333008 14.2871 0.333008 16.4996C0.333008 18.7121 1.11426 20.5996 2.67051 22.1621C4.22676 23.7246 6.12051 24.4996 8.33301 24.4996H13.933C14.5955 24.4996 15.133 23.9621 15.133 23.2996C15.133 22.6371 14.5955 22.0996 13.933 22.0996H8.38301C6.87676 22.1059 5.43301 21.5121 4.37051 20.4559Z\"\n          fill=\"#333333\"\n        />\n        <path\n          d=\"M11.3338 15.2988H21.3338C21.9963 15.2988 22.5338 15.8363 22.5338 16.4988C22.5338 17.1613 21.9963 17.6988 21.3338 17.6988H11.3338C10.6713 17.6988 10.1338 17.1613 10.1338 16.4988C10.1338 15.8363 10.6713 15.2988 11.3338 15.2988Z\"\n          fill=\"#333333\"\n        />\n        <path\n          d=\"M29.9947 10.8375C28.4322 9.275 26.551 8.5 24.3322 8.5H18.7322C18.0697 8.5 17.5322 9.0375 17.5322 9.7C17.5322 10.3625 18.0697 10.9 18.7322 10.9H24.2822C25.7822 10.8937 27.226 11.4875 28.2947 12.5437C29.3885 13.6375 29.9322 14.9625 29.9322 16.5062C29.9322 17.9875 29.3385 19.4062 28.2947 20.4562C27.201 21.55 25.8822 22.0937 24.3322 22.0937H18.7322C18.0697 22.0937 17.5322 22.6312 17.5322 23.2937C17.5322 23.9563 18.0697 24.4937 18.7322 24.4937H24.3322C26.5447 24.4937 28.4322 23.7125 29.9947 22.1562C31.5572 20.5937 32.3322 18.7125 32.3322 16.4937C32.3322 14.2875 31.551 12.4 29.9947 10.8375Z\"\n          fill=\"#333333\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_12832_8815\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" transform=\"translate(0.333008 0.5)\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default CopyLinkDarkIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,iBAAiB,EAAE,SAAS,EAA0B;IAC7D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBAAE,aAAU;;kCACX,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;wBAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAK9D;uCAEe", "debugId": null}}, {"offset": {"line": 2055, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/InterviewIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction InterviewIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} fill=\"#fff\" height=\"18\" viewBox=\"0 0 512.00001 512\" width=\"15\">\n      <path d=\"m191.730469 206.730469h25.917969l27.742187 27.738281c5.859375 5.859375 15.359375 5.859375 21.21875 0l27.742187-27.738281h25.917969c26.03125 0 47.128907-21.101563 47.128907-47.128907v-112.472656c0-26.027344-21.058594-47.128906-47.128907-47.128906h-128.539062c-26.03125 0-47.128907 21.058594-47.128907 47.128906v112.472656c0 26.027344 21.097657 47.128907 47.128907 47.128907zm16.488281-142.460938h95.980469c8.519531 0 15.390625 7.109375 14.980469 15.71875-.378907 8.070313-7.328126 14.28125-15.398438 14.28125h-95.980469c-8.519531 0-15.390625-7.109375-14.980469-15.730469.378907-8.070312 7.328126-14.269531 15.398438-14.269531zm0 48.199219h47.78125c8.519531 0 15.390625 7.109375 14.980469 15.722656-.378907 8.070313-7.332031 14.277344-15.402344 14.277344h-47.777344c-8.519531 0-15.390625-7.109375-14.980469-15.730469.378907-8.066406 7.328126-14.269531 15.398438-14.269531zm0 0\" />\n      <path d=\"m131.730469 336.328125h-21.402344v-72.296875c0-30.671875-24.96875-55.492188-55.769531-55.160156-30.359375.320312-54.558594 25.609375-54.558594 55.980468v79.519532c0 39.339844 31.890625 71.230468 71.230469 71.230468h39.097656v81.398438c0 8.28125 6.722656 15 15 15h21.402344v-160.671875c0-8.277344-6.710938-15-15-15zm0 0\" />\n      <path d=\"m320.269531 273.128906h-128.539062c-8.28125 0-15 6.722656-15 15v223.871094h158.539062v-223.871094c0-8.277344-6.71875-15-15-15zm0 0\" />\n      <path d=\"m110.335938 143.53125c0 30.46875-24.699219 55.167969-55.167969 55.167969s-55.167969-24.699219-55.167969-55.167969c0-30.464844 24.699219-55.164062 55.167969-55.164062s55.167969 24.699218 55.167969 55.164062zm0 0\" />\n      <path d=\"m512 143.53125c0 30.46875-24.699219 55.167969-55.167969 55.167969s-55.167969-24.699219-55.167969-55.167969c0-30.464844 24.699219-55.164062 55.167969-55.164062s55.167969 24.699218 55.167969 55.164062zm0 0\" />\n      <path d=\"m380.269531 336.328125h21.402344v-72.296875c0-30.671875 24.96875-55.492188 55.769531-55.160156 30.359375.320312 54.558594 25.609375 54.558594 55.980468v79.519532c0 39.339844-31.890625 71.230468-71.230469 71.230468h-39.097656v81.398438c0 8.28125-6.722656 15-15 15h-21.402344v-160.671875c0-8.277344 6.710938-15 15-15zm0 0\" />\n    </svg>\n  );\n}\n\nexport default InterviewIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,cAAc,EAAE,SAAS,EAA0B;IAC1D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,MAAK;QAAO,QAAO;QAAK,SAAQ;QAAoB,OAAM;;0BACtH,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 2125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2131, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/InterviewDetailModal.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */\n\"use client\";\nimport React from \"react\";\nimport { useTranslations } from \"next-intl\";\nimport { useRouter } from \"next/navigation\";\n\nimport \"../../styles/eventModal.scss\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport EditIcon from \"../svgComponents/EditIcon\";\nimport { IGetInterviewsResponse } from \"@/interfaces/interviewInterfaces\";\nimport { INTERVIEW_SCHEDULE_ROUND_TYPE, PERMISSION } from \"@/constants/commonConstants\";\nimport JoinMeetingIcon from \"../svgComponents/JoinMeetingIcon\";\nimport CopyLinkDarkIcon from \"../svgComponents/CopyLinkDarkIcon\";\nimport InterviewIcon from \"../svgComponents/InterviewIcon\";\nimport ROUTES from \"@/constants/routes\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { useSelector } from \"react-redux\";\n\ninterface IProps {\n  onClose: () => void;\n  onEdit: () => void;\n  interviewInfo: IGetInterviewsResponse | null;\n  attachments: string[];\n}\n\nconst InterviewDetailModal = ({ onClose, onEdit, interviewInfo, attachments }: IProps) => {\n  const start = new Date(interviewInfo?.start!);\n  const end = new Date(interviewInfo?.end!);\n  const durationMs = end.getTime() - start.getTime();\n  const durationMins = Math.round(durationMs / (1000 * 60));\n\n  const router = useRouter();\n  const t = useTranslations();\n  const tJob = useTranslations(\"jobRequirement\");\n\n  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];\n  const hasEditScheduledInterviewPermission = userPermissions.includes(PERMISSION.EDIT_SCHEDULED_INTERVIEWS);\n\n  console.log(\"hasEditScheduledInterviewPermission\", hasEditScheduledInterviewPermission);\n\n  return (\n    <>\n      <div className=\"modal theme-modal show-modal modal-lg interview-details-modal\">\n        <div className=\"modal-dialog modal-dialog-centered \">\n          <div className=\"modal-content\">\n            <div className=\"modal-header secondary-header\">\n              <h4 className=\"m-0\">{t(\"interview_details\")}</h4>\n              {!interviewInfo?.isEnded ? (\n                <Button type=\"submit\" className=\"clear-btn p-0 primary\" onClick={onEdit}>\n                  {hasEditScheduledInterviewPermission ? <EditIcon /> : null}\n                </Button>\n              ) : null}\n              <Button className=\"modal-close-btn\" onClick={onClose}>\n                <ModalCloseIcon />\n              </Button>\n            </div>\n            <div className=\"modal-body\" style={{ maxHeight: \"calc(100vh - 350px)\", overflowY: \"auto\" }}>\n              {interviewInfo?.roundType === INTERVIEW_SCHEDULE_ROUND_TYPE[1].value ? (\n                <div className=\"button-align mb-5\">\n                  <Button type=\"submit\" className=\"primary-btn rounded-md\">\n                    <JoinMeetingIcon className=\"me-3\" />\n                    {t(\"join_meeting\")}\n                  </Button>\n                  <Button className=\"dark-outline-btn rounded-md\">\n                    <CopyLinkDarkIcon className=\"me-3\" />\n                    {t(\"copy_link\")}\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"button-align mb-5\">\n                  <Button\n                    type=\"submit\"\n                    className=\"primary-btn rounded-md\"\n                    onClick={() =>\n                      router.push(\n                        `${ROUTES.INTERVIEW.PRE_INTERVIEW_QUESTIONS_OVERVIEW}?interviewId=${interviewInfo?.id}&jobApplicationId=${interviewInfo?.jobApplicationId}&interviewType=${encodeURIComponent(interviewInfo?.roundType!)}&resumeLink=${encodeURIComponent(interviewInfo?.resumeLink!)}&isEnded=${interviewInfo?.isEnded}&date=${encodeURIComponent(interviewInfo?.start!)}&time=${encodeURIComponent(interviewInfo?.end!)}`\n                      )\n                    }\n                  >\n                    <InterviewIcon className=\"me-3\" />\n                    {t(\"start_interview\")}\n                  </Button>\n                </div>\n              )}\n              <div className=\"row g-4\">\n                <div className=\"col-md-6\">\n                  <p>{tJob(\"job_title\")}</p>\n                  <h4>{interviewInfo?.jobTitle}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"job_id\")}</p>\n                  <h4>{interviewInfo?.jobUniqueId}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"candidate\")}</p>\n                  <h4 className=\"high-light-text\">{interviewInfo?.candidateName}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"interviewers\")}</p>\n                  <h4 className=\"high-light-text\">{interviewInfo?.interviewerName}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"interview_type\")}</p>\n                  <h4>{interviewInfo?.roundType}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"interview_date\")}</p>\n                  <h4>{new Date(interviewInfo?.start!).toLocaleDateString(\"en-US\", { year: \"numeric\", month: \"long\", day: \"numeric\" })}</h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"interview_duration\")}</p>\n                  <h4>\n                    {new Date(interviewInfo?.start!).toLocaleTimeString(\"en-US\", {\n                      hour: \"numeric\",\n                      minute: \"2-digit\",\n                      hour12: true,\n                    })}{\" \"}\n                    -{\" \"}\n                    {new Date(interviewInfo?.end!).toLocaleTimeString(\"en-US\", {\n                      hour: \"numeric\",\n                      minute: \"2-digit\",\n                      hour12: true,\n                    })}{\" \"}\n                    | {durationMins} {t(\"minutes\")}\n                  </h4>\n                </div>\n                <div className=\"col-md-6\">\n                  <p>{t(\"interview_round_number\")}</p>\n                  <h4>{interviewInfo?.roundNumber}</h4>\n                </div>\n              </div>\n              <div className=\"row g-4 mt-1\">\n                {interviewInfo?.resumeLink && (\n                  <div className=\"col-md-12\">\n                    <p>{t(\"candidate_resume_\")}</p>\n                    <a href={interviewInfo?.resumeLink} target=\"_blank\" className=\"text-primary color-primary\">\n                      {t(\"view_resume\")}\n                    </a>\n                  </div>\n                )}\n                {attachments?.length > 0 ? (\n                  <>\n                    <div className=\"col-md-6\">\n                      <p>{t(\"additional_document\")}</p>\n                      <a href={attachments[0]} target=\"_blank\" className=\"text-primary color-primary\">\n                        {t(\"document\")} 1\n                      </a>\n                      {attachments?.length > 1 ? (\n                        <a href={attachments[1]} target=\"_blank\" className=\"text-primary color-primary\">\n                          , {t(\"document\")} 2\n                        </a>\n                      ) : null}\n                      {attachments?.length > 2 ? (\n                        <a href={attachments[2]} target=\"_blank\" className=\"text-primary color-primary\">\n                          , {t(\"document\")} 3\n                        </a>\n                      ) : null}\n                    </div>\n                  </>\n                ) : null}\n                <div className=\"col-md-6\">\n                  <p>{t(\"interview_questions\")}</p>\n                  <a\n                    href={`${ROUTES.INTERVIEW.PRE_INTERVIEW_QUESTIONS_OVERVIEW}?interviewId=${interviewInfo?.id}&jobApplicationId=${interviewInfo?.jobApplicationId}&interviewType=${encodeURIComponent(interviewInfo?.roundType!)}&resumeLink=${encodeURIComponent(interviewInfo?.resumeLink!)}&isEnded=${interviewInfo?.isEnded}&date=${encodeURIComponent(interviewInfo?.start!)}&time=${encodeURIComponent(interviewInfo?.end!)}`}\n                    className=\"text-primary color-primary\"\n                  >\n                    {t(\"view_interview_questions\")}\n                  </a>\n                </div>\n                {interviewInfo?.description ? (\n                  <div className=\"col-md-12\">\n                    <p>{t(\"additional_notes\")}</p>\n                    <h5> {interviewInfo?.description}</h5>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default React.memo(InterviewDetailModal);\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;AAEzE;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAhBA;;;;;;;;;;;;;;;AAyBA,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAU;IACnF,MAAM,QAAQ,IAAI,KAAK,eAAe;IACtC,MAAM,MAAM,IAAI,KAAK,eAAe;IACpC,MAAM,aAAa,IAAI,OAAO,KAAK,MAAM,OAAO;IAChD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE;IAEvD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,OAAO,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE7B,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;IAChG,MAAM,sCAAsC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,yBAAyB;IAEzG,QAAQ,GAAG,CAAC,uCAAuC;IAEnD,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAO,EAAE;;;;;;gCACtB,CAAC,eAAe,wBACf,8OAAC,4IAAA,CAAA,UAAM;oCAAC,MAAK;oCAAS,WAAU;oCAAwB,SAAS;8CAC9D,oDAAsC,8OAAC,+IAAA,CAAA,UAAQ;;;;+CAAM;;;;;2CAEtD;8CACJ,8OAAC,4IAAA,CAAA,UAAM;oCAAC,WAAU;oCAAkB,SAAS;8CAC3C,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;4BAAa,OAAO;gCAAE,WAAW;gCAAuB,WAAW;4BAAO;;gCACtF,eAAe,cAAc,mIAAA,CAAA,gCAA6B,CAAC,EAAE,CAAC,KAAK,iBAClE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;;8DAC9B,8OAAC,sJAAA,CAAA,UAAe;oDAAC,WAAU;;;;;;gDAC1B,EAAE;;;;;;;sDAEL,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;;8DAChB,8OAAC,uJAAA,CAAA,UAAgB;oDAAC,WAAU;;;;;;gDAC3B,EAAE;;;;;;;;;;;;yDAIP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;wCACL,MAAK;wCACL,WAAU;wCACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,aAAa,EAAE,eAAe,GAAG,kBAAkB,EAAE,eAAe,iBAAiB,eAAe,EAAE,mBAAmB,eAAe,WAAY,YAAY,EAAE,mBAAmB,eAAe,YAAa,SAAS,EAAE,eAAe,QAAQ,MAAM,EAAE,mBAAmB,eAAe,OAAQ,MAAM,EAAE,mBAAmB,eAAe,MAAO;;0DAI/Y,8OAAC,oJAAA,CAAA,UAAa;gDAAC,WAAU;;;;;;4CACxB,EAAE;;;;;;;;;;;;8CAIT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,KAAK;;;;;;8DACT,8OAAC;8DAAI,eAAe;;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;8DAAI,eAAe;;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;oDAAG,WAAU;8DAAmB,eAAe;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;oDAAG,WAAU;8DAAmB,eAAe;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;8DAAI,eAAe;;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;8DAAI,IAAI,KAAK,eAAe,OAAQ,kBAAkB,CAAC,SAAS;wDAAE,MAAM;wDAAW,OAAO;wDAAQ,KAAK;oDAAU;;;;;;;;;;;;sDAEpH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;;wDACE,IAAI,KAAK,eAAe,OAAQ,kBAAkB,CAAC,SAAS;4DAC3D,MAAM;4DACN,QAAQ;4DACR,QAAQ;wDACV;wDAAI;wDAAI;wDACN;wDACD,IAAI,KAAK,eAAe,KAAM,kBAAkB,CAAC,SAAS;4DACzD,MAAM;4DACN,QAAQ;4DACR,QAAQ;wDACV;wDAAI;wDAAI;wDACL;wDAAa;wDAAE,EAAE;;;;;;;;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;8DAAI,eAAe;;;;;;;;;;;;;;;;;;8CAGxB,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,4BACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;oDAAE,MAAM,eAAe;oDAAY,QAAO;oDAAS,WAAU;8DAC3D,EAAE;;;;;;;;;;;;wCAIR,aAAa,SAAS,kBACrB;sDACE,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAG,EAAE;;;;;;kEACN,8OAAC;wDAAE,MAAM,WAAW,CAAC,EAAE;wDAAE,QAAO;wDAAS,WAAU;;4DAChD,EAAE;4DAAY;;;;;;;oDAEhB,aAAa,SAAS,kBACrB,8OAAC;wDAAE,MAAM,WAAW,CAAC,EAAE;wDAAE,QAAO;wDAAS,WAAU;;4DAA6B;4DAC3E,EAAE;4DAAY;;;;;;+DAEjB;oDACH,aAAa,SAAS,kBACrB,8OAAC;wDAAE,MAAM,WAAW,CAAC,EAAE;wDAAE,QAAO;wDAAS,WAAU;;4DAA6B;4DAC3E,EAAE;4DAAY;;;;;;+DAEjB;;;;;;;4DAGN;sDACJ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;oDACC,MAAM,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,gCAAgC,CAAC,aAAa,EAAE,eAAe,GAAG,kBAAkB,EAAE,eAAe,iBAAiB,eAAe,EAAE,mBAAmB,eAAe,WAAY,YAAY,EAAE,mBAAmB,eAAe,YAAa,SAAS,EAAE,eAAe,QAAQ,MAAM,EAAE,mBAAmB,eAAe,OAAQ,MAAM,EAAE,mBAAmB,eAAe,MAAO;oDACjZ,WAAU;8DAET,EAAE;;;;;;;;;;;;wCAGN,eAAe,4BACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAG,EAAE;;;;;;8DACN,8OAAC;;wDAAG;wDAAE,eAAe;;;;;;;;;;;;mDAErB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2695, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/CommonCalendar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useRef } from \"react\";\nimport FullCalendar from \"@fullcalendar/react\";\nimport dayGridPlugin from \"@fullcalendar/daygrid\";\nimport timeGridPlugin from \"@fullcalendar/timegrid\";\nimport interactionPlugin from \"@fullcalendar/interaction\";\nimport { DatesSetArg, DateSelectArg, EventClickArg, DayHeaderContentArg } from \"@fullcalendar/core\";\nimport { IGetInterviewsResponse } from \"@/interfaces/interviewInterfaces\";\n\ninterface CalendarProps {\n  handleDatesSet: (info: DatesSetArg) => void;\n  handleOnSelect: (info: DateSelectArg) => void;\n  interviews: Array<IGetInterviewsResponse>;\n  handleEventClick: (info: EventClickArg) => void;\n}\n\nconst CommonCalendar: React.FC<CalendarProps> = ({ handleDatesSet, handleOnSelect, interviews, handleEventClick }) => {\n  const calendarRef = useRef<FullCalendar>(null);\n\n  const renderEventContent = (eventInfo: EventClickArg) => {\n    return (\n      <div className=\"fc-event-content\">\n        <div className=\"fc-event-time\">\n          {new Date(eventInfo.event.start!).toLocaleTimeString(\"en-US\", {\n            hour: \"numeric\",\n            minute: \"2-digit\",\n            hour12: true,\n          })}\n        </div>\n        <div className=\"fc-event-title\">{eventInfo.event.title}</div>\n      </div>\n    );\n  };\n\n  const renderDayHeader = (args: DayHeaderContentArg) => {\n    // Only apply custom header in week view\n    const date = args.date;\n    const dayNumber = date.getDate();\n    const weekday = new Intl.DateTimeFormat(\"en-US\", { weekday: \"short\" }).format(date);\n    if (args.view.type === \"timeGridWeek\") {\n      return (\n        <div className=\"custom-day-header\">\n          <div className=\"day-number\">{dayNumber}</div>\n          <div className=\"weekday-name\">{weekday}</div>\n        </div>\n      );\n    } else {\n      return (\n        <div className=\"custom-day-header\">\n          <div className=\"weekday-name\">{weekday}</div>\n        </div>\n      );\n    }\n  };\n\n  return (\n    <div className=\"calendar-container\">\n      <FullCalendar\n        ref={calendarRef}\n        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}\n        headerToolbar={{\n          left: \"prev,next\",\n          center: \"title\",\n          right: \"dayGridMonth,timeGridWeek\",\n        }}\n        initialView=\"dayGridMonth\"\n        editable={false}\n        selectable={true}\n        fixedWeekCount={false}\n        dayMaxEvents={true}\n        weekends={true}\n        events={interviews}\n        select={handleOnSelect}\n        eventClick={handleEventClick}\n        height=\"66vh\"\n        datesSet={handleDatesSet}\n        eventContent={renderEventContent}\n        // dayHeaderFormat={{ weekday: \"short\", day: \"numeric\" }}\n        dayHeaderContent={renderDayHeader}\n      />\n    </div>\n  );\n};\n\nexport default React.memo(CommonCalendar);\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAgBA,MAAM,iBAA0C,CAAC,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE;IAC/G,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAEzC,MAAM,qBAAqB,CAAC;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,IAAI,KAAK,UAAU,KAAK,CAAC,KAAK,EAAG,kBAAkB,CAAC,SAAS;wBAC5D,MAAM;wBACN,QAAQ;wBACR,QAAQ;oBACV;;;;;;8BAEF,8OAAC;oBAAI,WAAU;8BAAkB,UAAU,KAAK,CAAC,KAAK;;;;;;;;;;;;IAG5D;IAEA,MAAM,kBAAkB,CAAC;QACvB,wCAAwC;QACxC,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,YAAY,KAAK,OAAO;QAC9B,MAAM,UAAU,IAAI,KAAK,cAAc,CAAC,SAAS;YAAE,SAAS;QAAQ,GAAG,MAAM,CAAC;QAC9E,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,gBAAgB;YACrC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAc;;;;;;kCAC7B,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;QAGrC,OAAO;YACL,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;;;;;;QAGrC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wJAAA,CAAA,UAAY;YACX,KAAK;YACL,SAAS;gBAAC,kJAAA,CAAA,UAAa;gBAAE,mJAAA,CAAA,UAAc;gBAAE,sJAAA,CAAA,UAAiB;aAAC;YAC3D,eAAe;gBACb,MAAM;gBACN,QAAQ;gBACR,OAAO;YACT;YACA,aAAY;YACZ,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,cAAc;YACd,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,QAAO;YACP,UAAU;YACV,cAAc;YACd,yDAAyD;YACzD,kBAAkB;;;;;;;;;;;AAI1B;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2841, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/ScheduleInterviewFromCalendar.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */\n\"use client\";\n// import Link from \"next/link\";\nimport React, { useCallback, useEffect, useRef, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { DateSelectArg, DatesSetArg, EventClickArg } from \"@fullcalendar/core\";\nimport { useTranslations } from \"next-intl\";\nimport { debounce } from \"lodash\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\n\nimport { formatTimeForInput, toastMessageError, toastMessageSuccess } from \"@/utils/helper\";\nimport CalenderEventModal from \"@/components/commonModals/CalendarEventModal\";\nimport style from \"../../../styles/conductInterview.module.scss\";\nimport { getInterviewers, getJobList, getMyInterviews, updateOrScheduleInterview } from \"@/services/interviewServices\";\nimport { scheduleInterviewValidation } from \"@/validations/interviewValidations\";\nimport { IGetInterviewersResponse, IGetInterviewsResponse, IGetJobListResponse, ScheduleInterviewFormValues } from \"@/interfaces/interviewInterfaces\";\nimport InterviewDetailModal from \"@/components/commonModals/InterviewDetailModal\";\nimport CommonCalendar from \"@/components/commonComponent/CommonCalendar\";\nimport { PERMISSION, ScheduleInterviewFormSubmissionType } from \"@/constants/commonConstants\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { useSelector } from \"react-redux\";\nimport toast from \"react-hot-toast\";\n\nexport const defaultValues = {\n  eventTitle: \"\",\n  date: \"\",\n  startTime: \"\",\n  endTime: \"\",\n  description: \"\",\n  interviewer: -1,\n  jobId: \"\",\n  jobTitle: -1,\n  interviewType: \"\",\n  candidate: -1,\n};\n\nconst ScheduleInterviewFromCalendar = () => {\n  const t = useTranslations();\n  const initialFetchCompleted = useRef(false);\n\n  const [interviews, setInterviews] = useState<IGetInterviewsResponse[]>([]);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [interviewers, setInterviewers] = useState<IGetInterviewersResponse[]>([]);\n  const [monthYear, setMonthYear] = useState<string>(`${String(new Date().getMonth() + 1).padStart(2, \"0\")}-${new Date().getFullYear()}`);\n  const [loader, setLoader] = useState(false);\n  const [jobLoader, setJobLoader] = useState(false);\n  const [fileUrls, setFileUrls] = useState<string[]>([]);\n  const [interviewInfo, setInterviewInfo] = useState<IGetInterviewsResponse | null>(null);\n  const [jobs, setJobs] = useState<IGetJobListResponse[]>([]);\n  const [formType, setFormType] = useState<(typeof ScheduleInterviewFormSubmissionType)[keyof typeof ScheduleInterviewFormSubmissionType]>(\n    ScheduleInterviewFormSubmissionType.SCHEDULE\n  );\n\n  const currentFileArrayLengthRef = useRef(fileUrls ? fileUrls?.length : 0);\n\n  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];\n\n  const hasScheduleInterviewPermission = userPermissions.includes(PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS);\n\n  const {\n    control,\n    handleSubmit,\n    reset,\n    setValue,\n    watch,\n    setError,\n    formState: { errors },\n    getValues,\n  } = useForm({\n    resolver: yupResolver(scheduleInterviewValidation(t)),\n  });\n\n  console.log(\"fileUrls==\", fileUrls);\n  console.log(\"currentFileArrayLengthRef.current==\", currentFileArrayLengthRef.current);\n\n  const getAllInterviews = async (monthYear: string) => {\n    try {\n      const result = await getMyInterviews(monthYear);\n\n      console.log(\"result\", result);\n      if (result?.data?.success) {\n        const events = result?.data?.data;\n        setInterviews(events);\n      }\n    } catch (error) {\n      console.log(error);\n    }\n  };\n\n  const onSubmit = async (data: ScheduleInterviewFormValues) => {\n    setLoading(true);\n    try {\n      const { eventTitle, date, startTime, endTime, description, jobTitle, candidate, interviewer, interviewType } = data;\n      console.log(\"inside onsubmit\");\n\n      const startDateTime = new Date(`${date}T${startTime}`);\n      const endDateTime = new Date(`${date}T${endTime}`);\n\n      // Convert to timestamp (milliseconds since epoch)\n      const startTimestamp = startDateTime.getTime();\n      const endTimestamp = endDateTime.getTime();\n      const scheduleAtTimestamp = new Date().getTime();\n\n      const updatePayload = {\n        title: eventTitle,\n        interviewerId: +interviewer,\n        scheduleAt: scheduleAtTimestamp,\n        startTime: startTimestamp,\n        endTime: endTimestamp,\n        description: description ?? \"\",\n        roundType: interviewType,\n        jobApplicationId: +candidate,\n        interviewId: +interviewInfo?.id!,\n        fileUrlArray: JSON.stringify(fileUrls),\n      };\n\n      const payload = {\n        ...updatePayload,\n        interviewId: undefined,\n        jobId: +jobTitle,\n      };\n\n      console.log(\"payload======>>>>\", payload);\n\n      const result = await updateOrScheduleInterview(formType === ScheduleInterviewFormSubmissionType.SCHEDULE ? payload : updatePayload);\n\n      if (result?.data?.success) {\n        toastMessageSuccess(t(result?.data?.message));\n        setIsModalOpen(false);\n        reset();\n        setFileUrls([]);\n        currentFileArrayLengthRef.current = 0;\n        getAllInterviews(monthYear);\n        setLoading(false);\n      } else {\n        setLoading(false);\n        toastMessageError(t(result?.data?.message ?? \"something_went_wrong\"));\n      }\n    } catch (error) {\n      console.log(error);\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDatesSet = (info: DatesSetArg) => {\n    console.log(\"Dates set:\", info);\n\n    // Get start date and add 10 days to ensure we're in the current month view\n    const startDate = new Date(info.start);\n    const adjustedDate = new Date(startDate);\n    adjustedDate.setDate(startDate.getDate() + 7);\n\n    // Format as MM-YYYY\n    const month = String(adjustedDate.getMonth() + 1).padStart(2, \"0\"); // +1 because months are 0-indexed\n    const year = adjustedDate.getFullYear();\n    const formattedDate = `${month}-${year}`;\n\n    if (formattedDate !== monthYear || !initialFetchCompleted.current) {\n      setMonthYear(formattedDate);\n      setInterviews([]);\n      getAllInterviews(formattedDate);\n\n      initialFetchCompleted.current = true;\n    }\n  };\n\n  const handleEventClick = (info: EventClickArg) => {\n    console.log(\"Event clicked:\", info.event);\n\n    const interviewInfoObj = interviews.find((interview) => Number(interview.id) === Number(info.event.id)) ?? null;\n\n    console.log(\"interviewInfoObj\", interviewInfoObj);\n\n    setInterviewInfo(interviewInfoObj);\n    const filesUrls = interviewInfoObj ? JSON.parse(interviewInfoObj?.attachments)?.fileUrls : [];\n    setFileUrls(filesUrls);\n    setIsViewModalOpen(true);\n  };\n\n  const handleOnSelect = (info: DateSelectArg) => {\n    console.log(\"Event selected:\", info);\n    if (!hasScheduleInterviewPermission) {\n      toast.dismiss();\n      console.log(\"inside if\", !hasScheduleInterviewPermission);\n      toastMessageError(t(\"you_dont_have_permission_to_schedule_interview\"));\n      return;\n    }\n    // Parse the selected date information\n    const startDate = new Date(info.start);\n    const endDate = new Date(info.end);\n\n    // Format date as YYYY-MM-DD for date input\n    const formattedDate = startDate.toLocaleDateString(\"en-CA\");\n    console.log(\"formattedDate\", formattedDate);\n\n    const startTime = formatTimeForInput(startDate);\n    const endTime = formatTimeForInput(endDate);\n\n    setValue(\"date\", formattedDate);\n    setValue(\"startTime\", startTime);\n    setValue(\"endTime\", endTime);\n    setIsModalOpen(true);\n    // getInterviewersList(\"\");\n    getJobs(\"\");\n  };\n\n  const handleModalClose = () => {\n    setIsModalOpen(false);\n    setFormType(ScheduleInterviewFormSubmissionType.SCHEDULE);\n    reset(defaultValues);\n  };\n\n  const watchedJobTitle = watch(\"jobTitle\");\n\n  const getInterviewersList = useCallback(\n    async (searchString: string) => {\n      setLoader(true);\n      try {\n        const response = await getInterviewers(searchString, watchedJobTitle?.toString());\n\n        if (response?.data?.success) {\n          setInterviewers(response?.data?.data);\n        }\n      } catch (error) {\n        console.error(\"Error fetching interviewers:\", error);\n      } finally {\n        setLoader(false);\n      }\n    },\n    [watchedJobTitle]\n  );\n\n  useEffect(() => {\n    getInterviewersList(\"\");\n  }, [watchedJobTitle]);\n\n  // console.log(\"watch\", watch(\"jobTitle\"));\n\n  const handleSearchInputChange = (event: string) => {\n    const searchString = event.trim();\n    console.log(\"searchString\", searchString);\n    getInterviewersList(searchString);\n  };\n\n  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);\n\n  const getJobs = useCallback(async (searchString: string) => {\n    setJobLoader(true);\n    try {\n      const response = await getJobList(searchString);\n\n      if (response?.data?.success) {\n        setJobs(response?.data?.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching interviewers:\", error);\n    } finally {\n      setJobLoader(false);\n    }\n  }, []);\n\n  const handleJobSearchInputChange = (event: string) => {\n    const searchString = event.trim();\n    console.log(\"searchString\", searchString);\n    getJobs(searchString);\n  };\n\n  const debouncedHandleJobSearchInputChange = debounce(handleJobSearchInputChange, 1000);\n\n  const onHandleEdit = () => {\n    console.log(\"inside edit\");\n    setIsViewModalOpen(false);\n    setFormType(ScheduleInterviewFormSubmissionType.UPDATE);\n    setIsModalOpen(true);\n\n    const startDate = interviewInfo?.start ? new Date(interviewInfo.start) : new Date();\n    const endDate = interviewInfo?.end ? new Date(interviewInfo.end) : new Date();\n\n    const startTime = formatTimeForInput(startDate);\n    const endTime = formatTimeForInput(endDate);\n\n    reset({\n      eventTitle: interviewInfo?.title,\n      date: startDate.toLocaleDateString(\"en-CA\"),\n      startTime,\n      endTime,\n      description: interviewInfo?.description,\n      interviewer: interviewInfo?.interviewerId,\n      jobId: interviewInfo?.jobUniqueId,\n      jobTitle: interviewInfo?.jobId,\n      interviewType: interviewInfo?.roundType,\n      candidate: interviewInfo?.jobApplicationId,\n    });\n\n    getInterviewersList(\"\");\n  };\n\n  const onHandleViewModelClose = () => {\n    setInterviewInfo(null);\n    setFileUrls([]);\n    setIsViewModalOpen(false);\n    reset(defaultValues);\n  };\n\n  return (\n    <div className={style.conduct_interview_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          {/* <div className=\"breadcrumb\">\n            <Link href=\"/\"> {t(\"home\")} </Link>\n            <Link href=\"/\">{t(\"calendar\")}</Link>\n          </div> */}\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                {t(\"dashboard_\")} - <span>{t(\"calendar\")}</span>\n              </h2>\n            </div>\n          </div>\n        </div>\n        <div className=\"inner-section\">\n          <CommonCalendar\n            handleDatesSet={handleDatesSet}\n            handleOnSelect={handleOnSelect}\n            interviews={interviews}\n            handleEventClick={handleEventClick}\n          />\n          {isModalOpen ? (\n            <CalenderEventModal\n              control={control}\n              onClose={handleModalClose}\n              handleSubmit={handleSubmit((data) => {\n                console.log(\"data\", data);\n                return onSubmit(data as ScheduleInterviewFormValues);\n              })}\n              setFileUrls={setFileUrls}\n              fileUrls={fileUrls}\n              loading={loading}\n              jobs={jobs}\n              jobLoader={jobLoader}\n              errors={errors}\n              currentFileArrayLengthRef={currentFileArrayLengthRef}\n              interviewers={interviewers}\n              loader={loader}\n              setValue={setValue}\n              getValues={getValues}\n              setError={setError}\n              interviewInfo={interviewInfo}\n              setJobs={setJobs}\n              formType={formType}\n              debouncedHandleSearchInputChange={debouncedHandleSearchInputChange}\n              debouncedHandleJobSearchInputChange={debouncedHandleJobSearchInputChange}\n            />\n          ) : null}\n          {isViewModalOpen ? (\n            <InterviewDetailModal onEdit={onHandleEdit} onClose={onHandleViewModelClose} interviewInfo={interviewInfo} attachments={fileUrls} />\n          ) : null}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ScheduleInterviewFromCalendar;\n"], "names": [], "mappings": "AAAA,8CAA8C,GAC9C,yEAAyE;;;;;AAEzE,gCAAgC;AAChC;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AApBA;;;;;;;;;;;;;;;;;AAsBO,MAAM,gBAAgB;IAC3B,YAAY;IACZ,MAAM;IACN,WAAW;IACX,SAAS;IACT,aAAa;IACb,aAAa,CAAC;IACd,OAAO;IACP,UAAU,CAAC;IACX,eAAe;IACf,WAAW,CAAC;AACd;AAEA,MAAM,gCAAgC;IACpC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAErC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,GAAG,OAAO,IAAI,OAAO,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,OAAO,WAAW,IAAI;IACtI,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAClF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC,mIAAA,CAAA,sCAAmC,CAAC,QAAQ;IAG9C,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,WAAW,UAAU,SAAS;IAEvE,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;IAEhG,MAAM,iCAAiC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,2BAA2B;IAEtG,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACrB,SAAS,EACV,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACV,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,8BAA2B,AAAD,EAAE;IACpD;IAEA,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,uCAAuC,0BAA0B,OAAO;IAEpF,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;YAErC,QAAQ,GAAG,CAAC,UAAU;YACtB,IAAI,QAAQ,MAAM,SAAS;gBACzB,MAAM,SAAS,QAAQ,MAAM;gBAC7B,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG;YAC/G,QAAQ,GAAG,CAAC;YAEZ,MAAM,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,EAAE,WAAW;YACrD,MAAM,cAAc,IAAI,KAAK,GAAG,KAAK,CAAC,EAAE,SAAS;YAEjD,kDAAkD;YAClD,MAAM,iBAAiB,cAAc,OAAO;YAC5C,MAAM,eAAe,YAAY,OAAO;YACxC,MAAM,sBAAsB,IAAI,OAAO,OAAO;YAE9C,MAAM,gBAAgB;gBACpB,OAAO;gBACP,eAAe,CAAC;gBAChB,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,aAAa,eAAe;gBAC5B,WAAW;gBACX,kBAAkB,CAAC;gBACnB,aAAa,CAAC,eAAe;gBAC7B,cAAc,KAAK,SAAS,CAAC;YAC/B;YAEA,MAAM,UAAU;gBACd,GAAG,aAAa;gBAChB,aAAa;gBACb,OAAO,CAAC;YACV;YAEA,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAAE,aAAa,mIAAA,CAAA,sCAAmC,CAAC,QAAQ,GAAG,UAAU;YAErH,IAAI,QAAQ,MAAM,SAAS;gBACzB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,QAAQ,MAAM;gBACpC,eAAe;gBACf;gBACA,YAAY,EAAE;gBACd,0BAA0B,OAAO,GAAG;gBACpC,iBAAiB;gBACjB,WAAW;YACb,OAAO;gBACL,WAAW;gBACX,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,QAAQ,MAAM,WAAW;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,cAAc;QAE1B,2EAA2E;QAC3E,MAAM,YAAY,IAAI,KAAK,KAAK,KAAK;QACrC,MAAM,eAAe,IAAI,KAAK;QAC9B,aAAa,OAAO,CAAC,UAAU,OAAO,KAAK;QAE3C,oBAAoB;QACpB,MAAM,QAAQ,OAAO,aAAa,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,kCAAkC;QACtG,MAAM,OAAO,aAAa,WAAW;QACrC,MAAM,gBAAgB,GAAG,MAAM,CAAC,EAAE,MAAM;QAExC,IAAI,kBAAkB,aAAa,CAAC,sBAAsB,OAAO,EAAE;YACjE,aAAa;YACb,cAAc,EAAE;YAChB,iBAAiB;YAEjB,sBAAsB,OAAO,GAAG;QAClC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,KAAK;QAExC,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAC,YAAc,OAAO,UAAU,EAAE,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE,MAAM;QAE3G,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,iBAAiB;QACjB,MAAM,YAAY,mBAAmB,KAAK,KAAK,CAAC,kBAAkB,cAAc,WAAW,EAAE;QAC7F,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,IAAI,CAAC,gCAAgC;YACnC,uJAAA,CAAA,UAAK,CAAC,OAAO;YACb,QAAQ,GAAG,CAAC,aAAa,CAAC;YAC1B,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;QACF;QACA,sCAAsC;QACtC,MAAM,YAAY,IAAI,KAAK,KAAK,KAAK;QACrC,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG;QAEjC,2CAA2C;QAC3C,MAAM,gBAAgB,UAAU,kBAAkB,CAAC;QACnD,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;QACrC,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEnC,SAAS,QAAQ;QACjB,SAAS,aAAa;QACtB,SAAS,WAAW;QACpB,eAAe;QACf,2BAA2B;QAC3B,QAAQ;IACV;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,YAAY,mIAAA,CAAA,sCAAmC,CAAC,QAAQ;QACxD,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM;IAE9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OAAO;QACL,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE,cAAc,iBAAiB;YAEtE,IAAI,UAAU,MAAM,SAAS;gBAC3B,gBAAgB,UAAU,MAAM;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,UAAU;QACZ;IACF,GACA;QAAC;KAAgB;IAGnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAgB;IAEpB,2CAA2C;IAE3C,MAAM,0BAA0B,CAAC;QAC/B,MAAM,eAAe,MAAM,IAAI;QAC/B,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,oBAAoB;IACtB;IAEA,MAAM,mCAAmC,CAAA,GAAA,kIAAA,CAAA,UAAQ,AAAD,EAAE,yBAAyB;IAE3E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjC,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE;YAElC,IAAI,UAAU,MAAM,SAAS;gBAC3B,QAAQ,UAAU,MAAM;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,6BAA6B,CAAC;QAClC,MAAM,eAAe,MAAM,IAAI;QAC/B,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,QAAQ;IACV;IAEA,MAAM,sCAAsC,CAAA,GAAA,kIAAA,CAAA,UAAQ,AAAD,EAAE,4BAA4B;IAEjF,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QACZ,mBAAmB;QACnB,YAAY,mIAAA,CAAA,sCAAmC,CAAC,MAAM;QACtD,eAAe;QAEf,MAAM,YAAY,eAAe,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI;QAC7E,MAAM,UAAU,eAAe,MAAM,IAAI,KAAK,cAAc,GAAG,IAAI,IAAI;QAEvE,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;QACrC,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEnC,MAAM;YACJ,YAAY,eAAe;YAC3B,MAAM,UAAU,kBAAkB,CAAC;YACnC;YACA;YACA,aAAa,eAAe;YAC5B,aAAa,eAAe;YAC5B,OAAO,eAAe;YACtB,UAAU,eAAe;YACzB,eAAe,eAAe;YAC9B,WAAW,eAAe;QAC5B;QAEA,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,iBAAiB;QACjB,YAAY,EAAE;QACd,mBAAmB;QACnB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,+JAAA,CAAA,UAAK,CAAC,sBAAsB;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAKb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;oCACE,EAAE;oCAAc;kDAAG,8OAAC;kDAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uJAAA,CAAA,UAAc;4BACb,gBAAgB;4BAChB,gBAAgB;4BAChB,YAAY;4BACZ,kBAAkB;;;;;;wBAEnB,4BACC,8OAAC,wJAAA,CAAA,UAAkB;4BACjB,SAAS;4BACT,SAAS;4BACT,cAAc,aAAa,CAAC;gCAC1B,QAAQ,GAAG,CAAC,QAAQ;gCACpB,OAAO,SAAS;4BAClB;4BACA,aAAa;4BACb,UAAU;4BACV,SAAS;4BACT,MAAM;4BACN,WAAW;4BACX,QAAQ;4BACR,2BAA2B;4BAC3B,cAAc;4BACd,QAAQ;4BACR,UAAU;4BACV,WAAW;4BACX,UAAU;4BACV,eAAe;4BACf,SAAS;4BACT,UAAU;4BACV,kCAAkC;4BAClC,qCAAqC;;;;;mCAErC;wBACH,gCACC,8OAAC,0JAAA,CAAA,UAAoB;4BAAC,QAAQ;4BAAc,SAAS;4BAAwB,eAAe;4BAAe,aAAa;;;;;mCACtH;;;;;;;;;;;;;;;;;;AAKd;uCAEe", "debugId": null}}, {"offset": {"line": 3219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3225, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/calendar/page.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport ScheduleInterviewFromCalendar from \"@/components/views/conductInterview/ScheduleInterviewFromCalendar\";\n\nconst page = () => {\n  return (\n    <div>\n      <ScheduleInterviewFromCalendar />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,OAAO;IACX,qBACE,8OAAC;kBACC,cAAA,8OAAC,gLAAA,CAAA,UAA6B;;;;;;;;;;AAGpC;uCAEe", "debugId": null}}, {"offset": {"line": 3247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}