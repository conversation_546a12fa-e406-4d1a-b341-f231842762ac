"use client";
import React, { use, useRef, useState } from "react";

import { yupResolver } from "@hookform/resolvers/yup";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

import UploadBox from "@/components/commonComponent/UploadBox";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textarea from "@/components/formElements/Textarea";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import DeleteDarkIcon from "@/components/svgComponents/DeleteDarkIcon";
import InfoIcon from "@/components/svgComponents/InfoIcon";
import PrimaryEyeIcon from "@/components/svgComponents/PrimaryEyeIcon";
import UploadFileIcon from "@/components/svgComponents/UploadFileIcon";
import { PDF_ADDITIONAL_SUBMISSION_LIMIT, PDF_FILE_TYPE } from "@/constants/commonConstants";
import ROUTES from "@/constants/routes";
import { addApplicantAdditionalInfo } from "@/services/CandidatesServices/candidatesApplicationServices";
import { toastMessageError, uploadFileOnS3 } from "@/utils/helper";
import { additionalSubmissionValidation } from "@/validations/additionalInfoVAlidation";

import style from "../../../styles/commonPage.module.scss";

interface FormValues {
  additionalInfo: string;
}

const AdditionalSubmission = ({ searchParams }: { searchParams: Promise<{ applicationId: string }> }) => {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const searchParamsPromise = use(searchParams);
  const [docInfo, setDocInfo] = useState<File | null>(null);
  const currentFileArrayLengthRef = useRef(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(additionalSubmissionValidation(t)),
    defaultValues: {
      additionalInfo: "",
    },
  });

  const handleClick = () => {
    const id = searchParamsPromise?.applicationId;
    console.log("searchParamsPromise", searchParamsPromise);
    console.log("id>>>>>>>>>>>>>>>>>>>>", id);

    if (!id) return;
    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${searchParamsPromise.applicationId}`);
  };

  const onSubmit = async (data: FormValues) => {
    console.log(">>>>>>>>>>>>>>>>applicationId", searchParamsPromise.applicationId);

    try {
      setIsLoading(true);

      let uploadedFileUrl: string | null = null;

      // If a file is uploaded, upload it to S3
      if (docInfo) {
        const fileNameArr = docInfo.name.split(".") || [];
        const filePath = `candidate-additional-info/${fileNameArr[0]}-${new Date().getTime()}.${fileNameArr[1]}`;
        uploadedFileUrl = (await uploadFileOnS3(docInfo, filePath)) as string;
      }

      const payload = {
        applicationId: searchParamsPromise.applicationId,
        description: data.additionalInfo,
        images: uploadedFileUrl, // will be null if no file uploaded
      };

      const response = await addApplicantAdditionalInfo(payload);
      if (response?.data?.success) {
        toast.success(t("additional_info_submitted_successfully"));

        // Reset file-related fields only if file was uploaded
        if (docInfo) {
          setDocInfo(null);
          if (inputRef.current) {
            inputRef.current.value = "";
          }
          currentFileArrayLengthRef.current = 0;
        }

        // Reset form fields
        reset({ additionalInfo: "" });
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsLoading(false);
    }
  };

  const fileClear = () => {
    setDocInfo(null);
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    currentFileArrayLengthRef.current = 0;
  };

  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsLoading(true);
    const files = e.target.files?.[0] || null;
    if (!files) {
      fileClear();
      setIsLoading(false);
      return null;
    }
    if (!files.name.includes(".pdf")) {
      toastMessageError(t("unsupported_file_type"));
      fileClear();
      setIsLoading(false);
      return null;
    }
    if (!PDF_FILE_TYPE.includes(files.type)) {
      toastMessageError(t("pdf_only"));
      fileClear();
      setIsLoading(false);
      return null;
    }
    // Validate file size (max 10MB)
    if (files.size > PDF_ADDITIONAL_SUBMISSION_LIMIT) {
      toastMessageError(t("invalid_size_format"));
      fileClear();
      setIsLoading(false);
      return null;
    }
    if (files.name.length > 50) {
      toastMessageError(t("pdf_name"));
      fileClear();
      setIsLoading(false);
      return null;
    }
    setDocInfo(files);

    fileClear();
    setIsLoading(false);
  };

  const clearDocument = () => {
    setDocInfo(null);
    fileClear();
  };

  const resetForm = () => {
    reset({ additionalInfo: "" });
    setDocInfo(null);
    fileClear();
  };

  return (
    <div className={`${style.resume_page} ${style.manual_upload_resume}`}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon onClick={() => router.back()} />
                {t("additional_info_submission")} <span>{t("before_hiring")}</span>
              </h2>
              <Button onClick={handleClick} className="clear-btn text-btn primary p-0 m-0">
                <PrimaryEyeIcon className="me-2" />
                {t("view_candidate_info")}
              </Button>
            </div>
            <p className="description">{t("additional_info_description")}</p>
          </div>
        </div>
        <div className={style.inner_page}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <InputWrapper>
              <InputWrapper.Label htmlFor="additionalInfo">
                {t("additional_info")}
                <sup>*</sup> <InfoIcon tooltip="Additional Info" id="AdditionalInfo" place="bottom" />
              </InputWrapper.Label>
              <Textarea rows={4} name="additionalInfo" control={control} placeholder={t("additional_info_placeholder")} className="form-control" />
              {errors.additionalInfo?.message && <InputWrapper.Error message={errors.additionalInfo.message as string} />}
            </InputWrapper>
            <InputWrapper>
              <InputWrapper.Label htmlFor="resume">{t("upload_doc")}</InputWrapper.Label>
              <UploadBox UploadBoxClassName="upload-card-sm" onChange={onFileChange} inputRef={inputRef} isLoading={isLoading} />
            </InputWrapper>
            {docInfo && (
              <div className="uploded-item">
                <div className="item-name">
                  <UploadFileIcon />
                  <p>{docInfo?.name}</p>
                </div>
                <DeleteDarkIcon onClick={clearDocument} className="delete-item" />
              </div>
            )}
            <div className="button-align py-5">
              <Button type="submit" className="primary-btn rounded-md" disabled={isLoading}>
                {isLoading ? t("Submitting") + "..." : t("submit")}
              </Button>
              <Button type="button" className="dark-outline-btn rounded-md" disabled={isLoading} onClick={resetForm}>
                {t("cancel")}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdditionalSubmission;
