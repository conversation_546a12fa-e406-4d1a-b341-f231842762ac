"use client";
import { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";

interface ArchiveCandidateModalProps {
  onClickCancel: () => void;
  applicationId: number;
  jobId: number;
  onSuccess?: (reason: string) => void;
}

const ArchiveCandidateModal: FC<ArchiveCandidateModalProps> = ({ onClickCancel, onSuccess }) => {
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ reason: string }>({
    defaultValues: { reason: "" },
    mode: "onSubmit",
    criteriaMode: "firstError",
    shouldFocusError: true,
    reValidateMode: "onChange",
    resolver: (values) => {
      const errors: Record<string, { type: string; message: string }> = {};
      if (!values.reason || values.reason.trim() === "") {
        errors.reason = { type: "required", message: "Please provide a reason" };
      }
      if (values.reason.trim().length < 5) {
        errors.reason = { type: "minLength", message: "Reason should be at least 5 characters long" };
      } else if (values.reason.trim().length > 50) {
        errors.reason = { type: "maxLength", message: "Reason should not exceed 50 characters" };
      }
      return { values, errors };
    },
  });

  const onSubmit = async (data: { reason: string }) => {
    if (!authData) return;
    try {
      setIsSubmitting(true);
      setError("");

      if (onSuccess) await onSuccess(data.reason);
    } catch (err) {
      console.error("Archive candidate error:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-between pb-0 ">
            <h2 className="m-0">Archive Candidate</h2>
            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <form onSubmit={handleSubmit(onSubmit)}>
              <InputWrapper>
                <InputWrapper.Label htmlFor="reason" required>
                  Please provide a reason
                </InputWrapper.Label>
                <Textarea rows={5} name="reason" control={control} placeholder="Enter reason for archiving candidate" className="form-control" />
                {errors.reason && <p className="text-danger mt-1">{errors.reason.message}</p>}
              </InputWrapper>

              {error && <div className="alert alert-danger my-3">{error}</div>}

              <div className="d-flex justify-content-end gap-2 mt-4">
                <Button type="button" className="secondary-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button type="submit" className="primary-btn rounded-md w-100" disabled={isSubmitting}>
                  {isSubmitting ? "Archiving..." : "Archive"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArchiveCandidateModal;
