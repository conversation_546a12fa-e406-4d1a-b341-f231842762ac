import * as Sentry from "@sentry/node";
import sendMail from "./sendgrid";

/* eslint-disable no-unused-vars */

export enum SubscriptionEmailType {
  PURCHASE = "purchase",
  CANCEL = "cancel",
  UPGRADE = "upgrade",
  DOWNGRADE = "downgrade",
  RENEW = "renew",
  PAYMENT_FAILED = "payment_failed",
  EXPIRED = "expired",
}

// For backwards compatibility
export const SUBSCRIPTION_EMAIL_TYPE = SubscriptionEmailType;

/**
 * Interface for subscription email parameters
 */
interface SubscriptionEmailParams {
  /** Recipient's email address */
  email: string;
  /** User's name for personalization */
  userName: string;
  /** Current subscription plan name */
  planName: string;
  /** Previous plan name (for upgrade/downgrade emails) */
  oldPlanName?: string;
  /** Type of email notification to send */
  emailType: SubscriptionEmailType | string;
  /** Subscription start date */
  startDate?: Date;
  /** Subscription cancellation date */
  cancellationDate?: Date;
  /** Subscription upgrade date */
  upgradeDate?: Date;
  /** Subscription downgrade date */
  downgradeDate?: Date;
  /** Billing interval (monthly/yearly) */
  interval?: string;
}

/**
 * Interface for email response
 */
interface EmailResponse {
  response?: any;
  error?: string;
}

/**
 * Helper to format dates consistently across the application
 * @param date - Date to format
 * @returns Formatted date string or default text if date is undefined
 */
const formatDate = (date?: Date, defaultText = "N/A"): string => {
  if (!date) return defaultText;

  try {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    Sentry.captureException(error);
    return defaultText;
  }
};

/**
 * Generates email templates based on subscription event type
 */
const generateEmailContent = (
  params: SubscriptionEmailParams
): { subject: string; htmlContent: string } => {
  const {
    userName,
    planName,
    oldPlanName,
    emailType,
    startDate,
    cancellationDate,
    upgradeDate,
    downgradeDate,
    interval,
  } = params;

  let subject = "";
  let htmlContent = "";

  switch (emailType) {
    case SubscriptionEmailType.PURCHASE:
      subject = "🎉 Subscription Confirmation – S9 InnerView";
      htmlContent = `
        <p>Dear <strong>${userName}</strong>,</p>
        <p>Thank you for purchasing the <strong>${planName}</strong> plan.</p>
        <p><strong>Start Date:</strong> ${formatDate(startDate)}<br />
        <strong>Billing Cycle:</strong> ${interval || "N/A"}</p>
        <p>We're excited to have you onboard!</p>`;
      break;

    case SubscriptionEmailType.CANCEL:
      subject = "❌ Subscription Cancelled – S9 InnerView";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your subscription for <strong>${planName}</strong> has been cancelled.</p>
        <p><strong>Cancellation Date:</strong> ${formatDate(cancellationDate)}<br />
        <strong>Next Billing Date:</strong> ${formatDate(startDate)}</p>
        <p>If this was a mistake, contact us to reactivate your plan.</p>`;
      break;

    case SubscriptionEmailType.RENEW:
      subject = "🔄 Subscription Renewed – S9 InnerView";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your <strong>${planName}</strong> subscription has been successfully renewed.</p>
        <p><strong>Renewal Date:</strong> ${formatDate(startDate)}<br />
        <strong>Billing Cycle:</strong> ${interval || "N/A"}</p>
        <p>Thank you for staying with us!</p>`;
      break;

    case SubscriptionEmailType.PAYMENT_FAILED:
      subject = "⚠️ Payment Failed – S9 InnerView Subscription";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>We were unable to process your payment for the <strong>${planName}</strong> subscription.</p>
        <p><strong>Attempt Date:</strong> ${formatDate(startDate)}<br />
        <strong>Billing Cycle:</strong> ${interval || "N/A"}</p>
        <p>Please update your payment method to avoid service interruption.</p>
        <p><a href="https://app.s9innerview.com/billing" style="color: #3182ce;">Update Payment Method</a></p>`;
      break;

    case SubscriptionEmailType.EXPIRED:
      subject = "⏰ Subscription Expired – S9 InnerView";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your <strong>${planName}</strong> subscription has expired.</p>
        <p><strong>Expired On:</strong> ${formatDate(startDate)}</p>
        <p>Your account may have been moved to a limited free plan. To continue enjoying premium features, please renew your subscription.</p>
        <p><a href="https://app.s9innerview.com/subscriptions" style="color: #3182ce;">Renew Subscription</a></p>`;
      break;

    case SubscriptionEmailType.UPGRADE:
      subject = "⬆️ Subscription Upgraded – S9 InnerView";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>You have successfully upgraded from <strong>${oldPlanName || "your previous plan"}</strong> to <strong>${planName}</strong>.</p>
        <p><strong>Upgrade Date:</strong> ${formatDate(upgradeDate)}<br />
        <strong>Billing Cycle:</strong> ${interval || "N/A"}</p>
        <p>Enjoy the enhanced features of your new plan!</p>`;
      break;

    case SubscriptionEmailType.DOWNGRADE:
      subject = "⬇️ Subscription Downgraded – S9 InnerView";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>Your subscription has been downgraded from <strong>${oldPlanName || "previous plan"}</strong> to <strong>${planName}</strong>.</p>
        <p><strong>Downgrade Date:</strong> ${formatDate(downgradeDate)}<br />
        <strong>Billing Cycle:</strong> ${interval || "N/A"}</p>
        <p>If you need more features again, feel free to upgrade anytime.</p>`;
      break;

    default:
      subject = "S9 InnerView Subscription Update";
      htmlContent = `
        <p>Hi <strong>${userName}</strong>,</p>
        <p>This is a general update regarding your subscription to <strong>${planName}</strong>.</p>`;
  }

  return { subject, htmlContent };
};

/**
 * Wraps email content in a consistent HTML template
 */
const wrapInEmailTemplate = (subject: string, htmlContent: string): string => `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>${subject}</title>
    </head>
    <body style="font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px; margin: 0;">
      <div style="max-width: 600px; margin: auto; background-color: #fff; border-radius: 8px; padding: 30px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <img src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/stratum-logo.png" alt="S9 Logo" style="width: 150px; margin-bottom: 20px;" />
        ${htmlContent}
        <p style="margin-top: 40px; color: #555;">Best regards,<br /><strong>Stratum 9 Team</strong></p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #888;">
          <p>If you have any questions, please contact our support team.</p>
        </div>
      </div>
      <footer style="text-align: center; font-size: 12px; color: #888; margin-top: 20px; padding: 10px 0;">
        © ${new Date().getFullYear()} STRATUM 9. All rights reserved.
      </footer>
    </body>
  </html>
  `;
// };

/**
 * Generates plain text version of the email for clients that don't support HTML
 */
const generateTextContent = (params: SubscriptionEmailParams): string => {
  const {
    userName,
    planName,
    emailType,
    startDate,
    cancellationDate,
    upgradeDate,
    downgradeDate,
    interval,
  } = params;

  const relevantDate =
    startDate || cancellationDate || upgradeDate || downgradeDate;

  return `
Hello ${userName},

This is your subscription update:

Plan: ${planName}
Status: ${emailType}
Date: ${formatDate(relevantDate)}
${interval ? `Billing Cycle: ${interval}` : ""}

Thanks,
Stratum 9 Team
  `;
};

/**
 * Sends subscription-related emails using templates based on event type.
 *
 * @param params - Subscription email parameters
 * @returns Promise with email sending response or error
 */
const sendSubscriptionEmail = async (
  params: SubscriptionEmailParams
): Promise<EmailResponse> => {
  try {
    // Validate required inputs
    if (!params.email || !params.userName || !params.planName) {
      throw new Error(
        "Missing required email parameters: email, userName, and planName are mandatory"
      );
    }

    // Validate email format
    // if (!isValidEmail(params.email)) {
    //   throw new Error(`Invalid email format: ${params.email}`);
    // }

    // Generate email content based on type
    const { subject, htmlContent } = generateEmailContent(params);

    // Wrap in standard template
    const fullHtml = wrapInEmailTemplate(subject, htmlContent);

    // Generate plain text version
    const textContent = generateTextContent(params);

    // Send the email
    const response = await sendMail({
      email: params.email.toLowerCase().trim(),
      subject,
      textContent,
      htmlContent: fullHtml,
    });

    // Log successful email sending (could use a proper logger here)
    console.log(
      `Subscription email sent successfully to ${params.email} for event: ${params.emailType}`
    );

    return { response };
  } catch (error: any) {
    // Structured error logging
    const errorDetails = {
      recipient: params.email,
      emailType: params.emailType,
      errorMessage: error.message,
      stack: error.stack,
    };

    console.error("Failed to send subscription email:", errorDetails);
    Sentry.captureException(error, {
      contexts: { email_params: { ...params } },
    });

    return { error: `Subscription email sending failed: ${error.message}` };
  }
};
// Export utility function to format dates for external use if needed
sendSubscriptionEmail.formatDate = formatDate;

export default sendSubscriptionEmail;
