import * as <PERSON><PERSON> from "joi";

export const cancelSubscriptionValidation = Joi.object({
  user_id: Joi.string().required().messages({
    "string.empty": "user_id_empty",
    "any.required": "user_id_req",
  }),
});

export const createCheckoutSessionValidation = Joi.object({
  plan_id: Joi.number().required().messages({
    "number.empty": "plan_id_empty",
    "any.required": "plan_id_req",
  }),
  pricing_id: Joi.number().required().messages({
    "number.empty": "pricing_id_empty",
    "any.required": "pricing_id_req",
  }),
});

export const validateSubscriptionDetailsValidation = Joi.object({
  plan_id: Joi.number().required().messages({
    "number.empty": "plan_id_empty",
    "any.required": "plan_id_req",
  }),
  pricing_id: Joi.number().required().messages({
    "number.empty": "pricing_id_empty",
    "any.required": "pricing_id_req",
  }),
});
