"use client";
import React, { useCallback, useEffect, useState } from "react";

import Link from "next/link";
import Image from "next/image";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { useTranslations } from "next-intl";

import { syncReduxStateToCookies } from "@/utils/syncReduxToCookies";
import Logo from "../../../public/assets/images/logo.svg";
import downArrow from "../../../public/assets/images/down-arrow.svg";
import User from "../../../public/assets/images/user.png";
import styles from "@/styles/header.module.scss";
import NotificationIcon from "../svgComponents/Notification";
import { logout } from "@/utils/helper";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { selectProfileData, setPermissions } from "@/redux/slices/authSlice";
import { getUserPermissions } from "@/services/authServices";

// Interface definitions moved to authServices.ts
import DataSecurityIcon from "../svgComponents/dataSecurityIcon";
import ROUTES from "@/constants/routes";
import { IUserData } from "@/interfaces/authInterfaces";
import EmployeeManagementIcon from "../svgComponents/EmployeeManagementIcon";
import RolesIcon from "../svgComponents/RolesIcon";
import ProfileIcon from "../svgComponents/ProfileIcon";
import LogoutIcon from "../svgComponents/LogoutIcon";
import Notifications from "../views/notification/Notifications";
import { RootState } from "@/redux/store";
import { getUnreadNotificationsCount } from "@/services/notificationServices/notificationService";
import { setHasUnreadNotification } from "@/redux/slices/notificationSlice";
import NavCalendarIcon from "../svgComponents/NavCalendarIcon";
import NavCandidatesIcon from "../svgComponents/NavCandidatesIcon";
import NavHomeIcon from "../svgComponents/NavHomeIcon";
import NavJobsIcon from "../svgComponents/NavJobsIcon";
import NavSettingsIcon from "../svgComponents/NavSettingsIcon";

const Header = () => {
  const [dropdown, SetDropdown] = useState(false);
  const userProfile: IUserData | null = useSelector(selectProfileData);

  const path = usePathname();
  const dispatch = useDispatch();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations("header");
  const tCommon = useTranslations("common");
  const tr = useTranslations();
  const pathname = usePathname();
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const hasUnreadNotification = useSelector((state: RootState) => state.notification.hasUnreadNotifications);

  const navigate = useRouter();

  // Handle clicks outside of dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        SetDropdown(false);
      }
    };

    // Add event listener when dropdown is open
    if (dropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Clean up event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdown]);

  // Toggle dropdown visibility
  const MenuDropdown = () => {
    SetDropdown(!dropdown);
  };

  // Function to fetch permissions using the authServices
  const fetchPermissions = useCallback(async () => {
    try {
      const response = await getUserPermissions();

      // Only update Redux store when success is true
      if (response.data?.success) {
        dispatch(setPermissions(response.data.data.rolePermissions));
        // Sync Redux state to cookies after updating permissions
        syncReduxStateToCookies(response.data.data.rolePermissions, true);
      } else {
        console.log("Permission fetch unsuccessful:", response.data?.message);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  }, [path, dispatch]);

  const getUserNotificationsUnreadStatus = useCallback(async () => {
    try {
      const response = await getUnreadNotificationsCount();
      if (response.data?.success) {
        const hasUnreadNotifications = response.data.data.count > 0;
        dispatch(setHasUnreadNotification(hasUnreadNotifications));
      } else {
        console.error("Failed to fetch unread notifications status:", response.data?.message);
      }
    } catch (error) {
      console.error("Error fetching unread notifications status:", error);
    }
  }, []);

  // Sync Redux state to cookies after mounting component
  useEffect(() => {
    syncReduxStateToCookies();
  }, []);

  useEffect(() => {
    // Check if this is first mount or a genuine route change
    fetchPermissions();
    getUserNotificationsUnreadStatus();
  }, [path, dispatch, fetchPermissions]);

  /**
   * Logs out the user if the access token is invalid.
   * If the access token is invalid, it logs out the user and shows a toast message.
   */

  // const logoutUser = async () => {
  //   const token = getAccessToken();
  //   if (!token) {
  //     onHandleLogout();
  //     toast.dismiss();
  //     toastMessageError(t("session_expired"));
  //   }
  // };

  const onHandleLogout = async () => {
    await logout(authData?.id);

    if (typeof window !== "undefined") {
      window.location.reload();
    }
  };

  return (
    <>
      <header
        className={styles.header}
        // className={`${styles.header} ${isVisible ? "" : `${styles.hidden}`}`}
      >
        <nav className="navbar navbar-expand-sm">
          <div className="container">
            <div className="d-flex align-items-center justify-content-between w-100">
              <Link className="navbar-brand" href={ROUTES.HOME}>
                <Image src={Logo} alt="logo" width={640} height={320} className={styles.logo} />
              </Link>
              {/* <Button className="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#collapsibleNavbar">
              <span className="navbar-toggler-icon"></span>
            </Button> */}
              <ul className="header_links">
                <li className={pathname === ROUTES.DASHBOARD ? "active" : ""} onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                  <NavHomeIcon /> Home
                </li>
                <li
                  className={pathname === ROUTES.SCREEN_RESUME.CANDIDATES ? "active" : ""}
                  onClick={() => navigate.push(ROUTES.SCREEN_RESUME.CANDIDATES)}
                >
                  <NavCandidatesIcon /> Candidates
                </li>
                <li className={pathname === ROUTES.INTERVIEW.CALENDAR ? "active" : ""} onClick={() => navigate.push(ROUTES.INTERVIEW.CALENDAR)}>
                  <NavCalendarIcon /> Calendar
                </li>
                <li className={pathname === ROUTES.JOBS.ACTIVE_JOBS ? "active" : ""} onClick={() => navigate.push(ROUTES.JOBS.ACTIVE_JOBS)}>
                  <NavJobsIcon /> Jobs
                </li>
                <li className={pathname === ROUTES.PROFILE.MY_PROFILE ? "active" : ""} onClick={() => navigate.push(ROUTES.PROFILE.MY_PROFILE)}>
                  <NavSettingsIcon /> Settings
                </li>
                <span></span>
              </ul>
              <div className={`collapse navbar-collapse justify-content-end ${styles.navbar_content}`} id="collapsibleNavbar">
                {/* <ul className={`navbar-nav ${styles.navbar_links}`}>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.JOBS.GENERATE_JOB || pathname === ROUTES.JOBS.CAREER_BASED_SKILLS || pathname === ROUTES.JOBS.CULTURE_BASED_SKILLS || pathname === ROUTES.JOBS.ROLE_BASED_SKILLS || pathname === ROUTES.JOBS.EDIT_SKILLS || pathname === ROUTES.JOBS.JOB_EDITOR || pathname === ROUTES.JOBS.HIRING_TYPE ? styles.active : ""}`}
                    href={ROUTES.JOBS.HIRING_TYPE}
                  >
                    {t("job_requirement_generations")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.JOBS.ACTIVE_JOBS || pathname?.startsWith(ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD) || pathname?.startsWith(ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION) ? styles.active : ""}`}
                    href={ROUTES.JOBS.ACTIVE_JOBS}
                  >
                    {t("resume_screening")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link className="nav-link" href="#">
                    {t("conduct_interview")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.DASHBOARD || pathname === ROUTES.SCREEN_RESUME.CANDIDATES || pathname === ROUTES.JOBS.ARCHIVE ? styles.active : ""}`}
                    href={ROUTES.DASHBOARD}
                  >
                    {tCommon("hm_dashboard")}
                  </Link>
                </li>
              </ul> */}

                <div className={styles.header_right}>
                  <NotificationIcon
                    hasNotification={hasUnreadNotification}
                    id="notification-icon-id"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      setIsNotificationOpen((prev) => !prev);
                    }}
                  />
                  <div className={`dropdown ${styles.user_drop}`}>
                    <button type="button" className={`dropdown-toggle ${styles.user_drop_btn}`} data-bs-toggle="dropdown" onClick={MenuDropdown}>
                      <div className={`${styles.circle_img}`}>
                        <Image src={userProfile?.image || User} alt="Profile" width={100} height={100} />
                      </div>
                      <div className={styles.admin_info}>
                        <h5>{`${userProfile?.first_name}`}</h5>
                      </div>
                      <Image src={downArrow} alt="downArrow" style={{ rotate: `${dropdown ? "180deg" : "0deg"}` }} />
                    </button>
                    {dropdown && (
                      <ul className={styles.dropdown_menu}>
                        <li>
                          <ProfileIcon />
                          <span
                            onClick={() => {
                              navigate.push(ROUTES.PROFILE.MY_PROFILE);
                              SetDropdown(false);
                            }}
                          >
                            {t("my_profile")}
                          </span>
                        </li>
                        <li>
                          <RolesIcon />
                          <span
                            onClick={() => {
                              navigate.push(ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS);
                              SetDropdown(false);
                            }}
                          >
                            {tr("roles_and_permissions")}
                          </span>
                        </li>

                        <li>
                          <EmployeeManagementIcon />
                          <span
                            onClick={() => {
                              navigate.push(ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT);
                              SetDropdown(false);
                            }}
                          >
                            {tr("employee_management")}
                          </span>
                        </li>
                        <li>
                          <LogoutIcon className="strokeSvg" />
                          <span onClick={() => onHandleLogout()}>Logout</span>
                        </li>
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>
      {isNotificationOpen ? <Notifications setIsNotificationOpen={setIsNotificationOpen} /> : null}

      {/* common pages information box for  Job Requirement Generation page */}
      {pathname === ROUTES.JOBS.GENERATE_JOB && (
        <div className="information-box">
          <DataSecurityIcon />
          <p>{tCommon("data_security_msg")}</p>
        </div>
      )}
    </>
  );
};

export default Header;
