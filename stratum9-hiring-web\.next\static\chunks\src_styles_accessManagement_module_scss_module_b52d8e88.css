/* [project]/src/styles/accessManagement.module.scss.module.css [app-client] (css) */
.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles, .accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan {
  list-style: none;
  padding: 0;
  margin: 0;
}

.accessManagement-module-scss-module__AuP3Pq__access_management {
  padding-top: 40px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .margin-add {
  margin-right: 70px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px dashed #333;
  padding-block: 20px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li button svg {
  width: 20px;
  height: 20px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li.accessManagement-module-scss-module__AuP3Pq__disabled_role {
  opacity: .75;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li.accessManagement-module-scss-module__AuP3Pq__disabled_role p {
  color: #333333b3;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li.accessManagement-module-scss-module__AuP3Pq__disabled_role button.accessManagement-module-scss-module__AuP3Pq__disabled {
  cursor: not-allowed;
  opacity: .6;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles li.accessManagement-module-scss-module__AuP3Pq__disabled_role button.accessManagement-module-scss-module__AuP3Pq__disabled svg {
  fill-opacity: .5;
  stroke-opacity: .5;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__user_roles_img {
  width: 100%;
  height: 500px;
  object-fit: contain;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_select_employee {
  color: #436eb6;
  background-color: #0000;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__tip_para {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 25px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__add_employee_height form {
  min-height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__add_employee_height .accessManagement-module-scss-module__AuP3Pq__form_card {
  border-radius: 30px;
  border: 2px solid #3333;
  padding: 20px;
  margin: 30px 0 20px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__folder_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  transition: transform .2s;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__folder_container:hover {
  transform: translateY(-3px);
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__department_card {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  padding: 0 8px;
  text-align: center;
  margin-bottom: 0;
  margin-top: 8px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__custom_dropdown {
  position: absolute;
  z-index: 1000;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px #00000026;
  width: 220px;
  padding: 8px 0;
  overflow: hidden;
  right: 0;
  bottom: calc(100% + 5px);
}

@media (height >= 768px) {
  .accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__custom_dropdown.accessManagement-module-scss-module__AuP3Pq__show_below {
    top: 25px;
    bottom: auto;
  }
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__dropdown_item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color .2s;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__dropdown_item:hover {
  background-color: #f5f5f5;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__dropdown_divider {
  height: 1px;
  background-color: #eee;
  margin: 0 8px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_selector {
  position: relative;
  display: inline-block;
  min-width: 180px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_select {
  color: #436eb6;
  background-color: #0000;
  font-size: 14px;
  min-width: 220px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23718096' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding: 0;
  cursor: pointer;
  border: none;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_select:focus {
  outline: none;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_select:disabled {
  opacity: .7;
  cursor: not-allowed;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_select option {
  background-color: #fff;
  color: #333;
  padding: 8px;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__selected_role {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #f5f7fa;
  border: 1px solid #e2e8f0;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  transition: all .2s;
  width: 100%;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__selected_role:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__selected_role .accessManagement-module-scss-module__AuP3Pq__dropdown_arrow {
  margin-left: 8px;
  font-size: 10px;
  color: #718096;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_dropdown {
  position: absolute;
  left: 0;
  z-index: 1000;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px #00000026;
  width: 100%;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  animation: .2s accessManagement-module-scss-module__AuP3Pq__fadeIn;
  bottom: calc(100% + 5px);
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_dropdown.accessManagement-module-scss-module__AuP3Pq__show_below {
  top: calc(100% + 5px);
  bottom: auto;
}

@keyframes accessManagement-module-scss-module__AuP3Pq__fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_option {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color .2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_option:last-child {
  border-bottom: none;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_option:hover {
  background-color: #f8fafc;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_option.accessManagement-module-scss-module__AuP3Pq__selected {
  background-color: #ebf8ff;
  color: #3182ce;
  font-weight: 500;
}

.accessManagement-module-scss-module__AuP3Pq__access_management .accessManagement-module-scss-module__AuP3Pq__role_option .accessManagement-module-scss-module__AuP3Pq__checkmark {
  color: #38b2ac;
  font-weight: bold;
  margin-left: 8px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page {
  padding-bottom: 80px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan_card {
  border-radius: 16px;
  box-shadow: 0 4px 12px #0000000f;
  transition: all .3s;
  cursor: pointer;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan_card.accessManagement-module-scss-module__AuP3Pq__selected_plan .accessManagement-module-scss-module__AuP3Pq__subscription_option {
  background: #cb993217;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan {
  max-height: 820px;
  overflow: auto;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__side_bar {
  box-shadow: none;
  padding: 15px 0;
  cursor: default;
  margin-top: 122px;
  max-height: max-content;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__side_bar li {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-start;
  text-align: left;
  padding-left: 10px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__side_bar li.accessManagement-module-scss-module__AuP3Pq__fixed_height {
  justify-content: flex-start;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__side_bar li .accessManagement-module-scss-module__AuP3Pq__benefit_text {
  max-width: calc(100% - 40px);
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__side_bar:hover {
  transition: none;
  transform: translateY(0);
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan.accessManagement-module-scss-module__AuP3Pq__subscription_benefit_text li {
  font-size: 1.5rem !important;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  padding: 10px 0;
  border-bottom: 1px solid #436eb633;
  text-align: center;
  min-height: 45px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li:last-child {
  border-bottom: none;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li.accessManagement-module-scss-module__AuP3Pq__sibling_height {
  min-height: 65px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li.accessManagement-module-scss-module__AuP3Pq__fixed_height {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li.accessManagement-module-scss-module__AuP3Pq__description_cell {
  height: auto;
  min-height: 80px;
  max-height: 80px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li.accessManagement-module-scss-module__AuP3Pq__plan_name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  height: auto;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li.accessManagement-module-scss-module__AuP3Pq__subscription_benefit_text {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  height: auto;
  margin-bottom: 20px;
  display: block;
  border-bottom: 0;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li svg {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  flex-shrink: 0;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_plan li .accessManagement-module-scss-module__AuP3Pq__benefit_text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section {
  margin-top: 30px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan {
  border-radius: 16px;
  border: 1px solid #d8d8d833;
  background: #fff;
  box-shadow: 0 4px 12px #0000000f;
  padding: 10px 20px;
  min-height: 75px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan h4 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan p {
  font-size: 11px;
  font-weight: 400;
  color: #333;
  margin-top: 5px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan span {
  font-size: 1.6rem;
  font-weight: 500;
  color: #436eb6;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan.accessManagement-module-scss-module__AuP3Pq__flex_content {
  gap: 10px;
  justify-content: space-between;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan.accessManagement-module-scss-module__AuP3Pq__active, .accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__select_plan_section .accessManagement-module-scss-module__AuP3Pq__select_plan:hover {
  transition: all .5s;
  background: #ebf2ff;
  border-color: #ebf2ff;
  box-shadow: none;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__section_title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__plan_option {
  flex: 1;
  padding: 15px;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #436eb64d;
  cursor: pointer;
  transition: all .3s;
  font-weight: 500;
  font-size: 1.6rem;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option {
  padding: 20px 15px;
  border-radius: 16px 16px 0 0;
  background: #436eb617;
  position: relative;
  text-align: center;
  min-height: 205px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option:last-child {
  margin-right: 0;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option .accessManagement-module-scss-module__AuP3Pq__plan_name {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
  margin-top: 10px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option .accessManagement-module-scss-module__AuP3Pq__plan_price {
  font-size: 2.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option .accessManagement-module-scss-module__AuP3Pq__price_type {
  font-size: 1.2rem;
  font-weight: 600;
  color: #3339;
  margin-bottom: 0;
  margin-top: 5px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option button {
  padding: 8px;
  border-radius: 16px;
  width: 100%;
  margin-top: 15px;
}

.accessManagement-module-scss-module__AuP3Pq__subscription_page .accessManagement-module-scss-module__AuP3Pq__subscription_option .accessManagement-module-scss-module__AuP3Pq__save_badge {
  position: absolute;
  top: -12px;
  right: 0;
  left: 0;
  background-color: #cb9932;
  color: #fff;
  padding: 5px 8px;
  border-radius: 8px;
  font-size: 1.2rem;
  font-weight: 500;
  max-width: 110px;
  margin: auto;
  z-index: 100;
}

/*# sourceMappingURL=src_styles_accessManagement_module_scss_module_b52d8e88.css.map*/