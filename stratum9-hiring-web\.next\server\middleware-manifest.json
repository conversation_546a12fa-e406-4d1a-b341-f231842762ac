{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c13982b5._.js", "server/edge/chunks/[root of the server]__9de05221._.js", "server/edge/chunks/edge-wrapper_27c01396.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1A56WE3Ho/F/caaKiTK7fIqyld/Q9QFc7i5eG2evCts=", "__NEXT_PREVIEW_MODE_ID": "754c7a94826e7f826adf9f7e526e25c4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cdbdb36fec62979dd8860356434d2b24b013bdd7fdbaa279b97a5932a9d7887a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "54046feced63c52ed62163ecc57c91b205f3f6052013749ad8f999c528ae882f"}}}, "sortedMiddleware": ["/"], "functions": {}}