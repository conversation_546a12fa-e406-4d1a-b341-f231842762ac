/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { useForm, useFieldArray, Controller, Resolver, UseFormSetError } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

import { formSchemaValidation } from "@/validations/screenResumeValidations";

import ROUTES from "@/constants/routes";
import { GENDER_OPTIONS } from "@/constants/screenResumeConstant";

import { IFormValues } from "@/interfaces/screenResumeInterfaces";

import Loader from "@/components/loader/Loader";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import HoldIcon from "@/components/svgComponents/HoldIcon";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import InfoIcon from "@/components/svgComponents/InfoIcon";

import { toastMessageSuccess, toastMessageError, uploadFileOnS3, dismissAllToasts } from "@/utils/helper";

import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import { PDF_FILE_SIZE_LIMIT, PDF_FILE_TYPE } from "@/constants/commonConstants";
import { uploadManualCandidate } from "@/services/screenResumeServices";
import Textbox from "@/components/formElements/Textbox";
import Select from "@/components/formElements/Select";
import Textarea from "@/components/formElements/Textarea";

/**
 * ManualUploadResume Component
 *
 * Allows hiring managers to manually upload candidate resumes and assessments.
 * Supports adding multiple candidates (up to 5) with validation for required fields.
 *
 * @returns {JSX.Element} The rendered ManualUploadResume component
 */

function ManualUploadResume({
  params,
  searchParams,
}: {
  params: Promise<{ jobId: string }>;
  searchParams: Promise<{ title: string; jobUniqueId: string }>;
}) {
  const router = useRouter();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const paramsPromise = React.use(params);
  const searchParamsPromise = React.use(searchParams);
  const t = useTranslations();
  // Initialize form with validation schema
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<IFormValues>({
    resolver: yupResolver(formSchemaValidation(t)) as unknown as Resolver<IFormValues>,
    defaultValues: {
      candidates: [{ name: "", email: "", gender: "", resume: null, assessment: null, additionalInfo: "" }],
    },
  });

  // Initialize field array for dynamic candidates
  const { fields, append, remove } = useFieldArray({
    control,
    name: "candidates",
  });

  // State for loading status during form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [candidateErrors, setCandidateErrors] = useState<Record<string, string>>({});
  const [candidateSuccess, setCandidateSuccess] = useState<Record<string, boolean>>({});
  const resumeFileRef = useRef<HTMLInputElement>(null);
  const assessmentFileRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {
      router.push(ROUTES.JOBS.ACTIVE_JOBS);
    }
  }, [paramsPromise.jobId, searchParamsPromise?.title]);

  /**
   * Checks for duplicate email addresses within the list of candidates.
   * Sets custom errors on the 'email' field of duplicate entries using RHF's setError.
   *
   * @param candidates Array of candidate objects from the form (IFormValues["candidates"]).
   * @param setError RHF's setError function from useForm. // Updated type hint in JSDoc
   * @param t Translation function.
   * @returns boolean - True if duplicates are found, false otherwise.
   */
  const checkForDuplicateEmails = (
    candidates: IFormValues["candidates"],
    setError: UseFormSetError<IFormValues>, // Use the specific RHF type
    t: (key: string) => string
  ): boolean => {
    const emailMap = new Map<string, number[]>(); // Map email to list of indices
    let hasDuplicates = false;

    // 1. Collect indices for each email
    candidates.forEach((candidate, index) => {
      const email = candidate.email?.trim().toLowerCase(); // Normalize email
      if (email) {
        // Only check non-empty emails
        if (!emailMap.has(email)) {
          emailMap.set(email, []);
        }
        emailMap.get(email)!.push(index); // Non-null assertion as we just set it
      }
    });

    // 2. Identify duplicates and set errors
    emailMap.forEach((indices) => {
      if (indices.length > 1) {
        hasDuplicates = true;
        // Set error for all occurrences of the duplicate email
        indices.forEach((index) => {
          // Use template literal type for the field name to satisfy TS
          setError(`candidates.${index}.email` as const, {
            type: "manual", // Indicate it's a manual/custom error
            message: t("email_duplicate_in_form"), // Add this key to your translations
          });
        });
      }
    });

    return hasDuplicates;
  };
  /**
   * Handles form submission for candidate uploads
   *
   * Processes the submitted form data, uploads files to S3, transforms File objects to URLs,
   * calls the API to upload candidates, shows success/error messages, and redirects on success.
   *
   * @param {IFormValues} data - The validated form data containing candidate information
   * @returns {Promise<void>}
   */
  const onSubmit = async (data: IFormValues) => {
    // Prevent multiple simultaneous submissions
    if (isSubmitting) {
      return;
    }
    const hasDuplicates = checkForDuplicateEmails(data.candidates, setError, t);
    if (hasDuplicates) {
      // Scroll to the first error or top of the form
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: "smooth" });
        // Optionally, find the first element with an error and focus/scroll to it
        // const firstErrorElement = document.querySelector('.auth-msg.error'); // Example
        // if (firstErrorElement) firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100); // Small delay to let DOM update
      return; // Stop submission if duplicates found
    }
    try {
      setIsSubmitting(true);
      // Clear any existing toast notifications before showing new ones
      dismissAllToasts();
      setCandidateErrors({}); // Clear previous errors
      // Note: Keep candidateSuccess states to preserve successful candidates

      // Transform candidates by uploading files to S3 and converting File objects to URLs
      // Filter out candidates that were already successfully processed
      const transformedCandidates = [];
      // Use string keys (field.id) for errors
      const uploadErrors: Record<string, string> = {};

      // Process candidates sequentially to handle errors properly
      for (let index = 0; index < data.candidates.length; index++) {
        const candidate = data.candidates[index];
        // --- CHANGE 1: Get the field.id for the current index ---
        const fieldId = fields[index]?.id; // Get the unique ID for this candidate's field

        // --- CHANGE 2: Check success using field.id ---
        if (candidateSuccess[fieldId] === true) {
          continue;
        }

        let hasUploadError = false;
        let errorMessage = "";

        try {
          // Generate unique file paths for each candidate's files
          const timestamp = Date.now() + index; // Add index to ensure uniqueness

          // Process resume file (required)
          let resumeUrl: string = "";
          if (candidate.resume) {
            const resumeFilePath = `manual-uploads/${candidate.resume.name}-resume-${timestamp}.pdf`;
            const uploadResult = await uploadFileOnS3(candidate.resume, resumeFilePath);
            console.log("resumeUrl=========>", uploadResult);
            if (!uploadResult) {
              hasUploadError = true;
              errorMessage = t("resume_upload_failed");
            } else {
              resumeUrl = uploadResult;
            }
          } else {
            hasUploadError = true;
            errorMessage = t("resume_file_required");
          }

          // Process assessment file (optional) - only if resume upload succeeded
          let assessmentUrl: string = "";
          if (!hasUploadError && candidate.assessment) {
            const assessmentFilePath = `manual-uploads/${candidate.assessment.name}-assessment-${timestamp}.pdf`;
            const uploadResult = await uploadFileOnS3(candidate.assessment, assessmentFilePath);
            console.log("assessmentUrl=========>", uploadResult);
            if (!uploadResult) {
              hasUploadError = true;
              errorMessage = t("assessment_file_upload_failed");
            } else {
              assessmentUrl = uploadResult;
            }
          }

          // Only add to transformedCandidates if no upload errors occurred
          if (!hasUploadError) {
            transformedCandidates.push({
              name: candidate.name,
              email: candidate.email,
              gender: candidate.gender,
              resume_file: resumeUrl,
              assessment_file: assessmentUrl,
              additional_details: candidate.additionalInfo,
            });
          } else {
            // --- CHANGE 3: Store the upload error using field.id ---
            if (fieldId) {
              // Ensure fieldId exists before storing
              uploadErrors[fieldId] = errorMessage;
            }
          }
        } catch (fileUploadError) {
          console.error(`Error uploading files for candidate ${index + 1}:`, fileUploadError);
          // --- CHANGE 4: Store caught error using field.id ---
          if (fieldId) {
            // Ensure fieldId exists before storing
            uploadErrors[fieldId] = (fileUploadError as Error)?.message || t("failed_to_upload_files");
          }
        }
      }

      // Set upload errors immediately on the UI
      if (Object.keys(uploadErrors).length > 0) {
        setCandidateErrors(uploadErrors); // Now keys are field IDs
      }

      const jobId = Number(paramsPromise.jobId);

      // Only proceed with backend submission if we have candidates with successful uploads
      if (transformedCandidates.length > 0) {
        // Upload candidates with transformed data (URLs instead of File objects)
        const response = await uploadManualCandidate({ candidates: transformedCandidates, job_id: jobId });

        if (response.data && response.data.results && response.data.results.length > 0) {
          // Process results to show per-candidate status
          // --- CHANGE 5: Initialize result state objects with string keys ---
          const errors: Record<string, string> = { ...uploadErrors }; // Start with upload errors (keys are field IDs now)
          const success: Record<string, boolean> = { ...candidateSuccess }; // Preserve existing success states (keys are field IDs)
          let successCount = Object.values(candidateSuccess).filter(Boolean).length; // Count existing successes
          let errorCount = Object.keys(uploadErrors).length; // Count upload errors

          // Create mapping from transformed candidates back to original indices
          let transformedIndex = 0;
          for (let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++) {
            // --- CHANGE 6: Get the field.id for the current original index ---
            const originalFieldId = fields[originalIndex]?.id;

            // Skip candidates that had upload errors (check using field.id)
            if (uploadErrors[originalFieldId]) {
              continue;
            }

            // Skip candidates that were already successfully processed (not sent to backend) (check using field.id)
            if (candidateSuccess[originalFieldId] === true) {
              continue;
            }

            // Process backend result for this candidate
            if (transformedIndex < response.data.results.length) {
              const result = response.data.results[transformedIndex];
              if (!result.success && result.error) {
                // --- CHANGE 7: Store backend error using field.id ---
                if (originalFieldId) {
                  // Ensure fieldId exists
                  errors[originalFieldId] = result.error;
                  success[originalFieldId] = false; // Explicitly mark as not successful
                  errorCount++;
                }
              } else if (result.success) {
                // --- CHANGE 8: Store backend success using field.id ---
                if (originalFieldId) {
                  // Ensure fieldId exists
                  success[originalFieldId] = true;
                  successCount++;
                }
              }
              transformedIndex++;
            }
          }

          // Update the states (preserve all candidates in UI, using field IDs)
          setCandidateErrors(errors); // Keys are now field IDs
          setCandidateSuccess(success); // Keys are now field IDs

          const skippedCount = Object.values(candidateSuccess).filter(Boolean).length; // Recalculate based on updated state if needed, or use previous count

          // Show consolidated toast messages
          const successMessages: string[] = [];
          const errorMessages: string[] = [];

          // Add skip message if applicable
          if (skippedCount > 0) {
            const skipKey = skippedCount === 1 ? "candidate_skipped_success" : "candidates_skipped_success";
            successMessages.push(`${skippedCount} ${t(skipKey)}`);
          }

          // Add processing results to appropriate message arrays
          if (successCount > 0) {
            const candidateKey = successCount === 1 ? "candidate_small" : "candidates_small";
            successMessages.push(`${successCount} ${t(candidateKey)} ${t("processed_successfully")}`);
          }

          if (errorCount > 0) {
            const errorKey = errorCount === 1 ? "candidate_failed_to_process" : "candidates_failed_to_process";
            errorMessages.push(`${errorCount} ${t(errorKey)}`);
          }

          // Display toast messages - show success and error toasts separately when both exist
          if (successMessages.length > 0) {
            toastMessageSuccess(successMessages.join(", "));
          }
          if (errorMessages.length > 0) {
            toastMessageError(errorMessages.join(", "));
          }

          // No redirect - user stays on the same page
        } else {
          toastMessageError(response.data?.message || t("something_went_wrong"));
        }
      } else {
        // No candidates were sent to backend - could be upload errors or all already successful
        const totalErrors = Object.keys(uploadErrors).length;
        const totalSuccessful = Object.values(candidateSuccess).filter(Boolean).length;
        if (totalErrors > 0) {
          const uploadErrorKey = totalErrors === 1 ? "candidate_failed_to_upload_files" : "candidates_failed_to_upload_files";
          const messagePrefix = totalErrors === 1 ? "" : `${t("all")} `;
          toastMessageError(`${messagePrefix} ${totalErrors} ${t(uploadErrorKey)}`);
        } else if (totalSuccessful === data.candidates.length) {
          const processedKey = totalSuccessful === 1 ? "candidate_already_processed" : "candidates_already_processed";
          const messagePrefix = totalSuccessful === 1 ? "" : `${t("all")} `;
          toastMessageSuccess(`${messagePrefix} ${totalSuccessful} ${t(processedKey)}`);
        } else {
          toastMessageError(t("no_candidates_processed"));
        }
        // Set upload errors if any (already done above)
        // if (totalErrors > 0) {
        //   setCandidateErrors(uploadErrors); // Already set
        // }
      }
    } catch (error) {
      console.error("Error uploading candidates:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsSubmitting(false);
    }
  };
  /**
   * Validates if a file meets PDF requirements
   * @param file - The file to validate
   * @returns boolean - True if validation passes, False otherwise
   */
  const handleFileChange = (file: File | null): boolean => {
    if (!file) {
      return false;
    }
    if (file.size > PDF_FILE_SIZE_LIMIT) {
      toastMessageError(t("pdf_size_five_mb"));
      return false;
    }
    // Validate file name contains PDF_FILE_NAME
    if (!file.name.includes(".pdf")) {
      toastMessageError(t("unsupported_file_type"));
      return false;
    }

    // Validate file type (PDF only)
    if (!PDF_FILE_TYPE.includes(file.type)) {
      toastMessageError(t("pdf_only"));
      return false;
    }

    // Validate file size (max 5MB)
    if (file.name.length > 50) {
      toastMessageError(t("pdf_name"));
      return false;
    }

    return true;
  };

  return (
    <div ref={scrollContainerRef} className={`${style.resume_page} ${style.manual_upload_resume}`}>
      <div className="container">
        <div className={style.inner_page}>
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon onClick={() => router.push(`${ROUTES.JOBS.ACTIVE_JOBS}`)} />
                  {t("manual_upload_resume")} <span>{searchParamsPromise?.title}</span>
                </h2>
                <Button
                  className="clear-btn p-0 color-primary"
                  onClick={() =>
                    router.push(
                      `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${paramsPromise.jobId}` +
                        `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`
                    )
                  }
                >
                  <HoldIcon className="me-2" PrimaryColor /> {t("view_panding_action")}
                </Button>
              </div>
            </div>
          </div>

          <form
            onSubmit={handleSubmit((data) => {
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: "smooth" });
              }, 50);
              onSubmit(data);
            })}
          >
            {fields.map((field, index) => (
              <div key={field.id} className={style.candidate_card}>
                <div className={`d-flex align-items-center justify-content-between ${style.candidate_card_header}`}>
                  <h3>
                    {t("candidate")} 0{index + 1}
                  </h3>
                  <div className="d-flex align-items-center gap-2">
                    {candidateSuccess[field.id] && (
                      <div className="text-success">
                        <p className="mb-0">
                          <strong>{t("candidate_upload_succ")}</strong>
                        </p>
                      </div>
                    )}
                    {candidateErrors[field.id] && (
                      <div className="text-danger">
                        <p className="mb-0">
                          <strong>{candidateErrors[field.id]}</strong>
                        </p>
                      </div>
                    )}
                    {index > 0 && (
                      <Button
                        type="button"
                        onClick={() => {
                          remove(index);
                          // Clear status for this candidate when removed
                          setCandidateErrors((prev) => {
                            const newState = { ...prev };
                            delete newState[field.id]; // Use field.id
                            return newState;
                          });
                          setCandidateSuccess((prev) => {
                            const newState = { ...prev };
                            delete newState[field.id]; // Use field.id
                            return newState;
                          });
                        }}
                        className="clear-btn p-0"
                      >
                        <DeleteIcon className="p-1" />
                      </Button>
                    )}
                  </div>
                </div>
                <div className={style.candidate_card_body}>
                  <div className="row">
                    {/* Name Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.name`} required>
                          {t("name")}
                        </InputWrapper.Label>
                        <InfoIcon tooltip="Enter the candidate’s full name." id={`name-info-${index}`} place="right" />
                        <Textbox
                          control={control}
                          name={`candidates.${index}.name`}
                          placeholder={t("enter_candidate_name")}
                          className="form-control"
                        />
                        {errors.candidates?.[index]?.name && <div className="auth-msg error">{errors.candidates[index]?.name?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Email Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.email`} required>
                          {t("email")}
                        </InputWrapper.Label>
                        <InfoIcon
                          tooltip="Provide a valid email address to contact the candidate regarding job-related updates."
                          id={`email-info-${index}`}
                          place="right"
                        />
                        <Textbox
                          control={control}
                          name={`candidates.${index}.email`}
                          placeholder={t("please_enter_candidate_email")}
                          className="form-control"
                        />
                        {errors.candidates?.[index]?.email && <div className="auth-msg error">{errors.candidates[index]?.email?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Gender Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.gender`} required>
                          {t("gender")}
                        </InputWrapper.Label>
                        <InfoIcon
                          tooltip="Select the candidate’s gender to support inclusive hiring analytics and reporting."
                          id={`gender-info-${index}`}
                          place="right"
                        />
                        <Select
                          control={control}
                          name={`candidates.${index}.gender`}
                          options={GENDER_OPTIONS}
                          className="w-100"
                          placeholder={t("select_gender")}
                        />
                        {errors.candidates?.[index]?.gender && <div className="auth-msg error">{errors.candidates[index]?.gender?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Resume Upload Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.resume`} required>
                          {t("upload_resume")}
                        </InputWrapper.Label>
                        <InfoIcon
                          tooltip="Upload the candidate’s latest resume in PDF format for evaluation"
                          id={`resume-info-${index}`}
                          place="right"
                        />
                        <div className={style.input_type_file}>
                          <Controller
                            name={`candidates.${index}.resume`}
                            control={control}
                            render={({ field }) => (
                              <input
                                id={`candidates.${index}.resume`}
                                type="file"
                                ref={resumeFileRef}
                                accept=".pdf"
                                onChange={(e) => {
                                  const file = e.target.files?.[0] || null;
                                  if (file) {
                                    if (handleFileChange(file)) {
                                      field.onChange(file);
                                    } else {
                                      e.target.value = "";
                                      field.onChange(null);
                                    }
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                              />
                            )}
                          />
                        </div>
                        {errors.candidates?.[index]?.resume && <div className="auth-msg error">{errors.candidates[index]?.resume?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Assessment Upload Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.assessment`}>
                          {t("upload_assesment")}
                          <InfoIcon
                            tooltip="Attach any completed assessments or test results relevant to the job role."
                            id={`assessment-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <div className={style.input_type_file}>
                          <Controller
                            name={`candidates.${index}.assessment`}
                            control={control}
                            render={({ field }) => (
                              <input
                                id={`candidates.${index}.assessment`}
                                type="file"
                                accept=".pdf"
                                ref={assessmentFileRef}
                                onChange={(e) => {
                                  const file = e.target.files?.[0] || null;
                                  if (file) {
                                    if (handleFileChange(file)) {
                                      field.onChange(file);
                                    } else {
                                      e.target.value = "";
                                      field.onChange(null);
                                    }
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                              />
                            )}
                          />
                        </div>
                        {errors.candidates?.[index]?.assessment && (
                          <div className="auth-msg error">{errors.candidates[index]?.assessment?.message}</div>
                        )}
                      </InputWrapper>
                    </div>

                    {/* Additional Information Field */}
                    <div className="col-md-12">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.additionalInfo`}>
                          {t("additional_info")}
                          <InfoIcon
                            tooltip="Add any extra details about the candidate like experience, availability, or preferences."
                            id={`additional-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <Textarea
                          control={control}
                          name={`candidates.${index}.additionalInfo`}
                          rows={6}
                          placeholder={t("enter_additional_info_about_candidate")}
                          className="form-control"
                        />
                        {errors.candidates?.[index]?.additionalInfo && (
                          <div className="auth-msg error">{errors.candidates[index]?.additionalInfo?.message}</div>
                        )}
                      </InputWrapper>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <div className={style.add_another_candidate_link}>
              <Button
                type="button"
                onClick={() => {
                  /**
                   * Handles adding another candidate to the form
                   *
                   * Checks if maximum limit (5) is reached before adding a new candidate entry.
                   * Shows a message if the limit is reached.
                   */
                  if (fields.length < 5) {
                    append({ name: "", email: "", gender: "", resume: null, assessment: null, additionalInfo: "" });
                  } else {
                    toastMessageSuccess(t("max_5_candidates"));
                  }
                }}
                className="clear-btn p-0 color-primary"
                disabled={fields.length >= 5}
              >
                {t("add_candidates_resume")} {fields.length >= 5 ? t("max_5_candidates") : ""}
              </Button>
            </div>

            <div className="button-align py-5">
              <Button type="submit" className="primary-btn rounded-md minWidth" disabled={isSubmitting}>
                {t("analyze")} {isSubmitting && <Loader />}
              </Button>
              <Button
                type="button"
                onClick={() => {
                  /**
                   * Resets the form to its default state
                   *
                   * Clears all form fields, errors, and success states, returning the form to its initial state
                   * with a single empty candidate entry.
                   */
                  reset();
                  setCandidateErrors({});
                  setCandidateSuccess({});
                }}
                className="dark-outline-btn rounded-md minWidth"
                disabled={isSubmitting}
              >
                {t("reset")}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default ManualUploadResume;
