import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";

/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */

export enum NotificationType {
  INTERVIEW_SCHEDULED = "Interview Scheduled",
  INTERVIEW_FEEDBACK_PENDING = "Interview Feedback Pending",
  JOB_POST_ARCHIVED = "Job Post Archived",
  CANDIDATE_PROMOTED = "Candidate Promoted",
  CANDIDATE_HIRED_REJECTED = "Candidate Hired/Rejected",
  FINAL_ASSESSMENT_SUBMITTED = "Final Assessment Submitted",
  SUBSCRIPTION_UPDATED = "Subscription Updated",
}

@Entity("notifications")
export default class NotificationsModal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: "int", name: "organization_id", nullable: false })
  @Index()
  organizationId: number;

  @Column({ type: "int", name: "user_id", nullable: false })
  @Index()
  userId: number;

  @Column({
    type: "enum",
    enum: NotificationType,
    default: NotificationType.INTERVIEW_SCHEDULED,
    nullable: false,
  })
  type: NotificationType;

  @Column({ type: "varchar", length: 50, nullable: false })
  title: string;

  @Column({ type: "varchar", length: 200, nullable: false })
  description: string;

  @Column({ type: "int", name: "related_id", nullable: true })
  relatedId: number;

  @Column({ type: "json", name: "additional_info", nullable: true })
  additionalInfo: Record<string, any>;

  @Column({
    type: "boolean",
    name: "is_watched",
    default: false,
  })
  isWatched: boolean;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
  })
  updatedTs: Date;
}
