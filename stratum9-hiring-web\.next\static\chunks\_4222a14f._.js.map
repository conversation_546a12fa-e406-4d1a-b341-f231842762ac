{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/accessManagement.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"access_management\": \"accessManagement-module-scss-module__AuP3Pq__access_management\",\n  \"active\": \"accessManagement-module-scss-module__AuP3Pq__active\",\n  \"add_employee_height\": \"accessManagement-module-scss-module__AuP3Pq__add_employee_height\",\n  \"benefit_text\": \"accessManagement-module-scss-module__AuP3Pq__benefit_text\",\n  \"checkmark\": \"accessManagement-module-scss-module__AuP3Pq__checkmark\",\n  \"custom_dropdown\": \"accessManagement-module-scss-module__AuP3Pq__custom_dropdown\",\n  \"department_card\": \"accessManagement-module-scss-module__AuP3Pq__department_card\",\n  \"description_cell\": \"accessManagement-module-scss-module__AuP3Pq__description_cell\",\n  \"disabled\": \"accessManagement-module-scss-module__AuP3Pq__disabled\",\n  \"disabled_role\": \"accessManagement-module-scss-module__AuP3Pq__disabled_role\",\n  \"dropdown_arrow\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_arrow\",\n  \"dropdown_divider\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_divider\",\n  \"dropdown_item\": \"accessManagement-module-scss-module__AuP3Pq__dropdown_item\",\n  \"fadeIn\": \"accessManagement-module-scss-module__AuP3Pq__fadeIn\",\n  \"fixed_height\": \"accessManagement-module-scss-module__AuP3Pq__fixed_height\",\n  \"flex_content\": \"accessManagement-module-scss-module__AuP3Pq__flex_content\",\n  \"folder_container\": \"accessManagement-module-scss-module__AuP3Pq__folder_container\",\n  \"form_card\": \"accessManagement-module-scss-module__AuP3Pq__form_card\",\n  \"plan_name\": \"accessManagement-module-scss-module__AuP3Pq__plan_name\",\n  \"plan_option\": \"accessManagement-module-scss-module__AuP3Pq__plan_option\",\n  \"plan_price\": \"accessManagement-module-scss-module__AuP3Pq__plan_price\",\n  \"price_type\": \"accessManagement-module-scss-module__AuP3Pq__price_type\",\n  \"role_dropdown\": \"accessManagement-module-scss-module__AuP3Pq__role_dropdown\",\n  \"role_option\": \"accessManagement-module-scss-module__AuP3Pq__role_option\",\n  \"role_select\": \"accessManagement-module-scss-module__AuP3Pq__role_select\",\n  \"role_select_employee\": \"accessManagement-module-scss-module__AuP3Pq__role_select_employee\",\n  \"role_selector\": \"accessManagement-module-scss-module__AuP3Pq__role_selector\",\n  \"save_badge\": \"accessManagement-module-scss-module__AuP3Pq__save_badge\",\n  \"section_title\": \"accessManagement-module-scss-module__AuP3Pq__section_title\",\n  \"select_plan\": \"accessManagement-module-scss-module__AuP3Pq__select_plan\",\n  \"select_plan_section\": \"accessManagement-module-scss-module__AuP3Pq__select_plan_section\",\n  \"selected\": \"accessManagement-module-scss-module__AuP3Pq__selected\",\n  \"selected_plan\": \"accessManagement-module-scss-module__AuP3Pq__selected_plan\",\n  \"selected_role\": \"accessManagement-module-scss-module__AuP3Pq__selected_role\",\n  \"show_below\": \"accessManagement-module-scss-module__AuP3Pq__show_below\",\n  \"sibling_height\": \"accessManagement-module-scss-module__AuP3Pq__sibling_height\",\n  \"side_bar\": \"accessManagement-module-scss-module__AuP3Pq__side_bar\",\n  \"subscription_benefit_text\": \"accessManagement-module-scss-module__AuP3Pq__subscription_benefit_text\",\n  \"subscription_option\": \"accessManagement-module-scss-module__AuP3Pq__subscription_option\",\n  \"subscription_page\": \"accessManagement-module-scss-module__AuP3Pq__subscription_page\",\n  \"subscription_plan\": \"accessManagement-module-scss-module__AuP3Pq__subscription_plan\",\n  \"subscription_plan_card\": \"accessManagement-module-scss-module__AuP3Pq__subscription_plan_card\",\n  \"tip_para\": \"accessManagement-module-scss-module__AuP3Pq__tip_para\",\n  \"user_roles\": \"accessManagement-module-scss-module__AuP3Pq__user_roles\",\n  \"user_roles_img\": \"accessManagement-module-scss-module__AuP3Pq__user_roles_img\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/EditIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype EditIconProps = {\n  className?: string;\n  fillNone?: boolean;\n  fillColor?: string;\n};\n\nfunction EditIcon({ className, fillNone, fillColor }: EditIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 27 26\" fill=\"none\" className={className}>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539Z\"\n        fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n      />\n      <mask id=\"mask0_11116_355\" style={{ maskType: \"luminance\" }} maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"24\" height=\"26\">\n        <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M0.666992 0H23.5744V25.2527H0.666992V0Z\" fill=\"white\" />\n      </mask>\n      <g mask=\"url(#mask0_11116_355)\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z\"\n          fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n        />\n      </g>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.6316 11.5792C19.4182 11.5792 19.2049 11.5112 19.0222 11.3725L11.7529 5.78851C11.3156 5.45251 11.2329 4.82584 11.5689 4.38584C11.9062 3.94851 12.5329 3.86717 12.9716 4.20317L20.2422 9.78584C20.6796 10.1218 20.7622 10.7498 20.4249 11.1885C20.2289 11.4445 19.9316 11.5792 19.6316 11.5792Z\"\n        fill={!fillNone ? \"#436EB6\" : fillColor ? fillColor : \"\"}\n      />\n    </svg>\n  );\n}\n\nexport default EditIcon;\n"], "names": [], "mappings": ";;;;;AAQA,SAAS,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAiB;IACjE,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;0BAExD,6LAAC;gBAAK,IAAG;gBAAkB,OAAO;oBAAE,UAAU;gBAAY;gBAAG,WAAU;gBAAiB,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;0BACpH,cAAA,6LAAC;oBAAK,UAAS;oBAAU,UAAS;oBAAU,GAAE;oBAA0C,MAAK;;;;;;;;;;;0BAE/F,6LAAC;gBAAE,MAAK;0BACN,cAAA,6LAAC;oBACC,UAAS;oBACT,UAAS;oBACT,GAAE;oBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;;;;;;0BAG1D,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAM,CAAC,WAAW,YAAY,YAAY,YAAY;;;;;;;;;;;;AAI9D;KA5BS;uCA8BM", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DeleteIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction DeleteIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"28\" viewBox=\"0 0 25 28\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z\"\n        fill=\"#D00000\"\n      />\n    </svg>\n  );\n}\n\nexport default DeleteIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,6LAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KAvBS;uCAyBM", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/user-role-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1118, height: 1136, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+ABQgM0ZHY5HBIjNPdwkKDCAkJSY7eHl5jq+wsLkkJCQlACIwSGB8mMX1coir8Xd/iNZ7gonZjJGU4XVzcaRIR0ZbACMuQVVidZXG3ePt/+bq8f/o7PL/ztDR6HRkW6lVTkp7AE5olMyPoLn09vf4//b29v/39/f/6OPg+4uGg/RSTUugABIbKThxeIGm8/P0//T09P/4+Pj/2NfW7k9YXN4zOz+fAAAAAABnaWqG8vP1//T09P/19fX/z9DQ6S84PMpETE/CAAAAAABiZGWG6erq/+nq6//S2OH/trvB5yoyNbMXHSCHAAAAAAEjJylsT1Za0VJaXtFGT1fRPkZMvhsiJocTGRxoUJGL9Am/ZT4AAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0IAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;KAD9C;AAIN;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,6LAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,6LAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,6LAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,6LAAC,+IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC,iKAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;KAtBwB;AAwBjB,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,6LAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP;MARgB", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/SearchIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction SearchIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\">\n      <g opacity=\"0.7\">\n        <path\n          d=\"M28.2109 18.8274C28.2109 20.6833 27.6605 22.4976 26.6295 24.0407C25.5984 25.5839 24.1329 26.7867 22.4182 27.497C20.7036 28.2072 18.8168 28.3931 16.9965 28.0311C15.1762 27.6691 13.5042 26.7755 12.1917 25.4632C10.8793 24.1509 9.9855 22.479 9.62331 20.6587C9.26111 18.8384 9.4468 16.9517 10.1569 15.237C10.867 13.5222 12.0696 12.0566 13.6127 11.0253C15.1557 9.99409 16.9699 9.44356 18.8259 9.44336C20.0583 9.44323 21.2786 9.68586 22.4173 10.1574C23.5559 10.6289 24.5905 11.3201 25.462 12.1915C26.3335 13.0629 27.0248 14.0974 27.4965 15.236C27.9681 16.3746 28.2109 17.595 28.2109 18.8274Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path d=\"M30.557 30.559L25.457 25.459\" stroke=\"#333333\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n      </g>\n    </svg>\n  );\n}\n\nexport default SearchIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,6LAAC;YAAE,SAAQ;;8BACT,6LAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;;;;;8BAEjB,6LAAC;oBAAK,GAAE;oBAA+B,QAAO;oBAAU,aAAY;oBAAM,eAAc;oBAAQ,gBAAe;;;;;;;;;;;;;;;;;AAIvH;KAfS;uCAiBM", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,6LAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,6LAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KAXM;uCAaS", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/roleService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { UserRoleForm } from \"@/interfaces/employeeInterface\";\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport * as http from \"@/utils/http\";\nimport { IRolePermission } from \"@/interfaces/roleInterface\";\n\nexport interface FindRoleResponse {\n  id: number;\n  name: string;\n  isDefaultRole: boolean;\n}\n\nexport interface UpdateRolePermissionsResponse {\n  id: number;\n  name: string;\n  isDefaultRole: number;\n  permission_count: string;\n  updated_ts: string;\n}\ninterface IPermission {\n  id: number;\n  name: string;\n  description: string;\n  selected: boolean;\n}\nexport interface RolePermissionResponse {\n  role_id: number;\n  role_name: string;\n  permissions: IPermission[];\n}\n\n/**\n * Get all roles associated with an organization\n * @returns Promise with API response\n */\nexport const findRoleList = (offset: number, limit: number): Promise<IApiResponseCommonInterface<FindRoleResponse[]>> => {\n  return http.get(endpoint.roles.GET_ROLES_WITH_PAGINATION, { offset, limit });\n};\n\nexport const findRole = (): Promise<IApiResponseCommonInterface<FindRoleResponse[]>> => {\n  return http.get(endpoint.roles.GET_ROLES);\n};\n\n/**\n * Add a new user role\n * @param roleData Role data to add\n * @returns Promise with API response\n */\nexport const addUserRole = (roleData: UserRoleForm): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {\n  return http.post(endpoint.roles.ADD_USER_ROLE, roleData);\n};\n\n/**\n * Update an existing user role\n * @param roleId ID of the role to update\n * @param roleData Updated role data\n * @returns Promise with API response\n */\nexport const updateUserRole = (roleId: number, roleData: UserRoleForm): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {\n  return http.put(`${endpoint.roles.UPDATE_USER_ROLE}/${roleId}`, roleData);\n};\n\n/**\n * Delete a user role\n * @param roleId ID of the role to delete\n * @returns Promise with API response\n */\nexport const deleteUserRole = (roleId: number): Promise<IApiResponseCommonInterface<FindRoleResponse>> => {\n  return http.remove(`${endpoint.roles.DELETE_USER_ROLE}/${roleId}`);\n};\n\n/**\n * Get role permissions with counts and last modified date\n * @param organizationId ID of the organization\n * @returns Promise with API response\n */\nexport const getRolePermissions = (offset: number, limit: number, search?: string): Promise<IApiResponseCommonInterface<IRolePermission[]>> => {\n  return http.get(endpoint.roles.GET_ROLE_PERMISSIONS, { offset, limit, search });\n};\n\n/**\n * Get detailed permissions for a specific role\n * @param roleId ID of the role to get permissions for\n * @returns Promise with API response\n */\nexport const getRolePermissionsById = (roleId: number): Promise<IApiResponseCommonInterface<RolePermissionResponse>> => {\n  const url = endpoint.roles.GET_ROLE_PERMISSIONS_BY_ID.replace(\":roleId\", roleId.toString());\n  return http.get(url);\n};\n\n/**\n * Update permissions for a specific role\n * @param roleId ID of the role to update permissions for\n * @param permissionIds Array of permission IDs\n * @returns Promise with API response\n */\nexport const updateRolePermissions = (\n  roleId: number,\n  permissionIds: number[]\n): Promise<IApiResponseCommonInterface<UpdateRolePermissionsResponse>> => {\n  const url = endpoint.roles.UPDATE_ROLE_PERMISSIONS.replace(\":roleId\", roleId.toString());\n  return http.put(url, { permissionIds });\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGA;;;AAgCO,MAAM,eAAe,CAAC,QAAgB;IAC3C,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,yBAAyB,EAAE;QAAE;QAAQ;IAAM;AAC5E;AAEO,MAAM,WAAW;IACtB,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,SAAS;AAC1C;AAOO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAS,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,aAAa,EAAE;AACjD;AAQO,MAAM,iBAAiB,CAAC,QAAgB;IAC7C,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,GAAG,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,EAAE;AAClE;AAOO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,uHAAA,CAAA,SAAW,AAAD,EAAE,GAAG,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ;AACnE;AAOO,MAAM,qBAAqB,CAAC,QAAgB,OAAe;IAChE,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,oBAAoB,EAAE;QAAE;QAAQ;QAAO;IAAO;AAC/E;AAOO,MAAM,yBAAyB,CAAC;IACrC,MAAM,MAAM,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,WAAW,OAAO,QAAQ;IACxF,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE;AAClB;AAQO,MAAM,wBAAwB,CACnC,QACA;IAEA,MAAM,MAAM,+HAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,uBAAuB,CAAC,OAAO,CAAC,WAAW,OAAO,QAAQ;IACrF,OAAO,CAAA,GAAA,uHAAA,CAAA,MAAQ,AAAD,EAAE,KAAK;QAAE;IAAc;AACvC", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/utils/validationSchema.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\n// Regex patterns\nexport const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\nexport const NAME_REGEX = /^[a-zA-Z0-9\\s.'-]+$/;\n\n// Name validation regex - only allows alphabetic characters (including Unicode letters) and spaces\n// This pattern supports international names while rejecting digits and special characters\nexport const CANDIDATE_NAME_REGEX = /^[\\p{L}\\s]+$/u;\n\n// Employee validation schema\nexport const employeeValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    firstName: yup\n      .string()\n      .trim()\n      .required(translation(\"first_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_first_name\"))\n      .max(50, translation(\"max_first_name\")),\n    lastName: yup\n      .string()\n      .trim()\n      .required(translation(\"last_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(1, translation(\"min_last_name\"))\n      .max(50, translation(\"max_last_name\")),\n    email: yup\n      .string()\n      .trim()\n      .required(translation(\"email_req\"))\n      .email(translation(\"email_val_msg\"))\n      .matches(EMAIL_REGEX, translation(\"email_val_msg\")),\n    department: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"department_req\"))\n      .min(1, \"Department must be selected\"),\n    role: yup\n      .number()\n      .transform((value) => (isNaN(value) ? undefined : value))\n      .required(translation(\"role_req\"))\n      .min(1, \"Role must be selected\"),\n    // orderOfInterview: yup.string().required(translation(\"order_interview_req\")),\n  });\n\n// Employee array validation schema\nexport const employeesValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    employees: yup.array().of(employeeValidationSchema(translation)).required(\"At least one employee is required\"),\n  });\n\n// Department validation schema\nexport const departmentValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"department_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_department_name\"))\n      .max(50, translation(\"max_department_name\")),\n  });\n\n// Role validation schema\nexport const roleValidationSchema = (translation: (key: string) => string) =>\n  yup.object().shape({\n    name: yup\n      .string()\n      .trim()\n      .required(translation(\"role_name_req\"))\n      .matches(NAME_REGEX, {\n        message: translation(\"valid_name\"),\n        excludeEmptyString: true,\n      })\n      .min(2, translation(\"min_role_name\"))\n      .max(50, translation(\"max_role_name\")),\n  });\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGO,MAAM,cAAc;AACpB,MAAM,aAAa;AAInB,MAAM,uBAAuB;AAG7B,MAAM,2BAA2B,CAAC,cACvC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,sIAAA,CAAA,SACF,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,mBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,mBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,UAAU,CAAA,GAAA,sIAAA,CAAA,SACD,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;QACvB,OAAO,CAAA,GAAA,sIAAA,CAAA,SACE,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,cACrB,KAAK,CAAC,YAAY,kBAClB,OAAO,CAAC,aAAa,YAAY;QACpC,YAAY,CAAA,GAAA,sIAAA,CAAA,SACH,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,mBACrB,GAAG,CAAC,GAAG;QACV,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,SAAS,CAAC,CAAC,QAAW,MAAM,SAAS,YAAY,OACjD,QAAQ,CAAC,YAAY,aACrB,GAAG,CAAC,GAAG;IAEZ;AAGK,MAAM,4BAA4B,CAAC,cACxC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,WAAW,CAAA,GAAA,sIAAA,CAAA,QAAS,AAAD,IAAI,EAAE,CAAC,yBAAyB,cAAc,QAAQ,CAAC;IAC5E;AAGK,MAAM,6BAA6B,CAAC,cACzC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,wBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,wBACnB,GAAG,CAAC,IAAI,YAAY;IACzB;AAGK,MAAM,uBAAuB,CAAC,cACnC,CAAA,GAAA,sIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,sIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,OAAO,CAAC,YAAY;YACnB,SAAS,YAAY;YACrB,oBAAoB;QACtB,GACC,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,IAAI,YAAY;IACzB", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/accessManagement/RolesPermissions.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\n\"use client\";\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport styles from \"../../../styles/accessManagement.module.scss\";\nimport Button from \"@/components/formElements/Button\";\nimport EditIcon from \"@/components/svgComponents/EditIcon\";\nimport DeleteIcon from \"@/components/svgComponents/DeleteIcon\";\nimport Image from \"next/image\";\nimport userRolesImg from \"../../../../public/assets/images/user-role-img.png\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport Textbox from \"@/components/formElements/Textbox\";\nimport SearchIcon from \"@/components/svgComponents/SearchIcon\";\nimport { useForm } from \"react-hook-form\";\nimport UserRoleModal from \"@/components/commonModals/UserRoleModal\";\nimport EditPermissionsModal from \"@/components/commonModals/EditPermissionsModal\";\nimport { findRoleList, FindRoleResponse, getRolePermissions, UpdateRolePermissionsResponse } from \"@/services/roleService\";\nimport { IRolePermission } from \"@/interfaces/roleInterface\";\nimport { toastMessageError, toastMessageSuccess } from \"@/utils/helper\";\nimport { useTranslations } from \"next-intl\";\nimport Skeleton from \"react-loading-skeleton\";\nimport { DEFAULT_LIMIT } from \"@/constants/commonConstants\";\nimport InfiniteScroll from \"react-infinite-scroll-component\";\nimport TableSkeleton from \"../skeletons/TableSkeleton\";\nimport { debounce } from \"lodash\";\n\n// Interface for role permission data\nexport const ROLE_ALTER_MODE = {\n  ADD: \"add\",\n  EDIT: \"edit\",\n  DELETE: \"delete\",\n};\n\nexport const VIEW_MODE = {\n  ROLES: \"roles\",\n  PERMISSIONS: \"permissions\",\n};\n\nconst RolesPermissions = () => {\n  const t = useTranslations();\n  const { control } = useForm();\n  const [roleModalConfig, setRoleModalConfig] = useState<{\n    show: boolean;\n\n    mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE];\n    role: FindRoleResponse | null;\n  }>({ show: false, mode: ROLE_ALTER_MODE.ADD, role: null });\n  const [showEditPermissionsModal, setShowEditPermissionsModal] = useState(false);\n  const [selectedRole, setSelectedRole] = useState<{ id: number; name: string } | null>(null);\n  const [roles, setRoles] = useState<FindRoleResponse[]>([]);\n  const [rolePermissions, setRolePermissions] = useState<IRolePermission[]>([]);\n  const [isLoadingRoles, setIsLoadingRoles] = useState(true);\n  const [isLoadingRolesPermissions, setIsLoadingRolesPermissions] = useState(true);\n  const [hasMore, setHasMore] = useState(true);\n  const [hasMorePermission, setHasMorePermission] = useState(true);\n  const [activeView, setActiveView] = useState<(typeof VIEW_MODE)[keyof typeof VIEW_MODE]>(VIEW_MODE.ROLES);\n\n  const [searchTerm, setSearchTerm] = useState<string>(\"\");\n  const [offsets, setOffsets] = useState<{ roles: number; permissions: number }>({ roles: 0, permissions: 0 });\n\n  // Define fetch functions with useCallback to prevent unnecessary re-renders\n  const fetchRoles = async (currentOffset = offsets.roles, reset = false) => {\n    try {\n      setIsLoadingRoles(true);\n      const response = await findRoleList(currentOffset, DEFAULT_LIMIT);\n      if (response?.data?.success && Array.isArray(response.data.data)) {\n        const rolesFetched = response.data.data;\n        setRoles((prevRoles) => (reset ? rolesFetched : [...prevRoles, ...rolesFetched]));\n\n        if (rolesFetched.length < DEFAULT_LIMIT) {\n          setHasMore(false);\n        } else {\n          setHasMore(true);\n        }\n\n        setOffsets((prev) => ({ ...prev, roles: currentOffset + rolesFetched.length }));\n      } else {\n        setHasMore(false);\n      }\n    } catch (error) {\n      console.error(\"Error fetching roles:\", error);\n      setHasMore(false);\n    } finally {\n      setIsLoadingRoles(false);\n    }\n  };\n\n  const fetchRolePermissions = async (currentOffset = offsets.permissions, search: string, reset = false) => {\n    try {\n      setIsLoadingRolesPermissions(true);\n      const response = await getRolePermissions(currentOffset, DEFAULT_LIMIT, search);\n      if (response.data?.success) {\n        const rolePermissionsFetched = response.data.data;\n\n        setRolePermissions((prevRoles) => (reset ? rolePermissionsFetched : [...prevRoles, ...rolePermissionsFetched]));\n\n        if (rolePermissionsFetched.length < DEFAULT_LIMIT) {\n          setHasMorePermission(false);\n        } else {\n          setHasMorePermission(true);\n        }\n\n        setOffsets((prev) => ({ ...prev, permissions: currentOffset + rolePermissionsFetched.length }));\n      } else {\n        toastMessageError(response?.data?.message ? response.data.message : t(\"failed_load_roles\"));\n      }\n    } catch (error) {\n      console.error(error);\n      setHasMorePermission(false);\n      toastMessageError(t(\"unexpected_error\"));\n    } finally {\n      setIsLoadingRolesPermissions(false);\n    }\n  };\n\n  // Fetch data when component mounts or view changes\n  useEffect(() => {\n    if (activeView === VIEW_MODE.ROLES) {\n      if (roles.length === 0) {\n        fetchRoles(0, true);\n      }\n    } else {\n      if (rolePermissions.length === 0) {\n        fetchRolePermissions(0, \"\", true);\n      }\n    }\n    // Do NOT reset offsets when switching views\n  }, [activeView]);\n\n  const openRoleModal = useCallback(\n    (mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE], role: { id: number; name: string; isDefaultRole: boolean } | null = null) => {\n      setRoleModalConfig({\n        show: true,\n        mode,\n        role,\n      });\n    },\n    []\n  );\n\n  const closeRoleModal = () => {\n    setRoleModalConfig({\n      show: false,\n      mode: ROLE_ALTER_MODE.ADD,\n      role: null,\n    });\n  };\n\n  const handleRoleSuccess = (message?: string, responseData?: FindRoleResponse) => {\n    if (!responseData) {\n      return;\n    }\n\n    // Extract the role data from the response\n    const roleData = responseData;\n\n    // Update the roles state based on the operation type\n    if (roleModalConfig.mode === ROLE_ALTER_MODE.ADD && roleData) {\n      // Add the new role to the beginning of the list\n      setRoles((prevRoles) => [\n        {\n          id: roleData.id,\n          name: roleData.name,\n          isDefaultRole: false,\n        },\n        ...prevRoles,\n      ]);\n    } else if (roleModalConfig.mode === ROLE_ALTER_MODE.EDIT && roleData) {\n      // Update the existing role in the list\n      setRoles((prevRoles) => prevRoles.map((role) => (role.id === roleData.id ? { ...role, name: roleData.name } : role)));\n    } else if (roleModalConfig.mode === ROLE_ALTER_MODE.DELETE && roleData) {\n      // Remove the deleted role from the list\n      setRoles((prevRoles) => prevRoles.filter((role) => role.id !== roleData.id));\n    }\n\n    if (message) {\n      toastMessageSuccess(message);\n    }\n  };\n\n  const handleEditClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {\n    openRoleModal(ROLE_ALTER_MODE.EDIT, role);\n  };\n\n  const handleDeleteClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {\n    openRoleModal(ROLE_ALTER_MODE.DELETE, role);\n  };\n\n  const handlePermissionsClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {\n    setSelectedRole(role);\n    setShowEditPermissionsModal(true);\n  };\n\n  const handlePermissionsSuccess = (message?: string, updatedRole?: UpdateRolePermissionsResponse) => {\n    if (updatedRole) {\n      // Update the role in the rolePermissions array without making an API call\n      setRolePermissions((prevRoles) =>\n        prevRoles.map((role) =>\n          role.id === updatedRole.id\n            ? {\n                ...role,\n                name: updatedRole.name,\n                permission_count: updatedRole.permission_count,\n                updated_ts: updatedRole.updated_ts,\n                isDefaultRole: !!updatedRole.isDefaultRole,\n              }\n            : role\n        )\n      );\n    } else {\n      // If no updated role data is provided, fetch from API as before\n      if (activeView === VIEW_MODE.ROLES) {\n        fetchRoles(offsets.roles);\n      } else {\n        fetchRolePermissions(offsets.permissions, searchTerm);\n      }\n    }\n\n    if (message) {\n      toastMessageSuccess(message);\n    }\n  };\n\n  const handleSearchInputChange = (event: string) => {\n    const searchString = event.trim();\n    setSearchTerm(searchString);\n    setRolePermissions([]); // Clear previous results on new search\n    setHasMorePermission(true);\n    setOffsets((prev) => ({ ...prev, permissions: 0 }));\n    fetchRolePermissions(0, searchString, true);\n  };\n\n  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 1000);\n\n  return (\n    <>\n      <section className={styles.access_management}>\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className={activeView === VIEW_MODE.ROLES ? \"col-md-7\" : \"col-md-12\"}>\n              <div className=\"common-page-head-section\">\n                <div className=\"main-heading\">\n                  <h2>\n                    {t(\"access_management\", { defaultValue: \"Access\" })} <span>{t(\"management\", { defaultValue: \"Management\" })}</span>\n                  </h2>\n                </div>\n              </div>\n              <div className=\"button-align justify-content-between mt-5 mb-5\">\n                <div className=\"button-align\">\n                  <Button\n                    className={`${activeView === VIEW_MODE.ROLES ? \"primary-btn\" : \"dark-outline-btn\"} rounded-md button-sm minWidth`}\n                    onClick={() => setActiveView(VIEW_MODE.ROLES)}\n                  >\n                    {t(\"user_roles\", { defaultValue: \"User Roles\" })}\n                  </Button>\n                  <Button\n                    className={`${activeView === VIEW_MODE.PERMISSIONS ? \"primary-btn\" : \"dark-outline-btn\"} rounded-md button-sm`}\n                    onClick={() => setActiveView(VIEW_MODE.PERMISSIONS)}\n                  >\n                    {t(\"user_permissions\", { defaultValue: \"User Permissions\" })}\n                  </Button>\n                </div>\n\n                {activeView === VIEW_MODE.PERMISSIONS && (\n                  <InputWrapper className=\"mb-0 w-50\">\n                    <div className=\"icon-align right\">\n                      <Textbox\n                        className=\"form-control w-100\"\n                        control={control}\n                        name=\"search\"\n                        type=\"text\"\n                        placeholder={t(\"search_user_role\", { defaultValue: \"Search using user role\" })}\n                        onChange={(e) => debouncedHandleSearchInputChange(e.target.value)}\n                      >\n                        <InputWrapper.Icon>\n                          <SearchIcon />\n                        </InputWrapper.Icon>\n                      </Textbox>\n                    </div>\n                  </InputWrapper>\n                )}\n              </div>\n\n              {activeView === VIEW_MODE.ROLES ? (\n                // Roles View\n                <div className=\"common-card margin-add\">\n                  <div className=\"card-header\">\n                    <h3>{t(\"user_role\")}</h3>\n                    <Button className=\"dark-outline-btn rounded-md\" onClick={() => openRoleModal(ROLE_ALTER_MODE.ADD)}>\n                      {t(\"add_new_role\")}\n                    </Button>\n                  </div>\n                  <div className=\"card-body\">\n                    {\n                      <InfiniteScroll\n                        dataLength={roles.length}\n                        next={() => fetchRoles(offsets.roles)}\n                        hasMore={hasMore}\n                        height={window.innerHeight - 500}\n                        className=\"pe-4\"\n                        loader={\n                          isLoadingRoles && (\n                            <ul className={`mt-3 ${styles.user_roles}`}>\n                              <Skeleton height={30} width=\"100%\" count={6} borderRadius={4} style={{ margin: \"10px 0\" }} />\n                            </ul>\n                          )\n                        }\n                        endMessage={\n                          !isLoadingRoles && roles.length ? (\n                            <div className=\"text-center py-4\">\n                              <p>{t(\"no_more_roles_to_fetch\")}</p>\n                            </div>\n                          ) : null\n                        }\n                      >\n                        <ul className={styles.user_roles}>\n                          {roles.length\n                            ? [...roles]\n                                .sort((a, b) => a.name.localeCompare(b.name))\n                                .map((role) => (\n                                  <li key={role.id} className={role.isDefaultRole ? styles.disabled_role : \"\"}>\n                                    <p>{role.name}</p>\n                                    <div className=\"button-align\">\n                                      <Button\n                                        className={`clear-btn p-0 ${role.isDefaultRole ? \"disabled\" : \"\"}`}\n                                        onClick={() => !role.isDefaultRole && handleEditClick(role)}\n                                        disabled={role.isDefaultRole}\n                                      >\n                                        <EditIcon />\n                                      </Button>\n                                      <Button\n                                        className={`clear-btn p-0 ${role.isDefaultRole ? \"disabled\" : \"\"}`}\n                                        onClick={() => !role.isDefaultRole && handleDeleteClick(role)}\n                                        disabled={role.isDefaultRole}\n                                      >\n                                        <DeleteIcon />\n                                      </Button>\n                                    </div>\n                                  </li>\n                                ))\n                            : !isLoadingRoles && (\n                                <p className=\"text-center p-5\">\n                                  <strong>{t(\"no_roles_found\")}</strong>\n                                </p>\n                              )}\n                        </ul>\n                      </InfiniteScroll>\n                    }\n                  </div>\n                </div>\n              ) : (\n                // Roles View End\n                // Permissions View\n                <div className=\"table-responsive mt-5\">\n                  <InfiniteScroll\n                    dataLength={rolePermissions.length}\n                    next={() => fetchRolePermissions(offsets.permissions, searchTerm)}\n                    hasMore={hasMorePermission}\n                    height={window.innerHeight - 300}\n                    loader={\n                      isLoadingRolesPermissions && (\n                        <table className=\"table w-100\">\n                          <TableSkeleton rows={10} cols={4} colWidths=\"120,80,100,24,24\" />\n                        </table>\n                      )\n                    }\n                    endMessage={\n                      !isLoadingRolesPermissions && rolePermissions.length ? (\n                        <table className=\"table w-100\">\n                          <tbody>\n                            <tr>\n                              <td colSpan={5} style={{ textAlign: \"center\", backgroundColor: \"#fff\" }}>\n                                {t(\"no_more_roles_to_fetch\")}\n                              </td>\n                            </tr>\n                          </tbody>\n                        </table>\n                      ) : null\n                    }\n                  >\n                    <table className=\"table overflow-auto mb-0\">\n                      <thead>\n                        <tr>\n                          <th style={{ width: \"25%\" }}>{t(\"user_role\")}</th>\n                          <th style={{ width: \"25%\" }} className=\"text-center\">\n                            {t(\"permission_counts\")}\n                          </th>\n                          <th style={{ width: \"25%\" }} className=\"text-center\">\n                            {t(\"last_modified\")}\n                          </th>\n                          <th style={{ width: \"25%\" }} className=\"text-center\">\n                            {t(\"actions\")}\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {rolePermissions.length\n                          ? [...rolePermissions]\n                              .sort((a, b) => a.name.localeCompare(b.name))\n                              .map((role) => (\n                                <tr key={role.id} className={role.isDefaultRole ? \"text-muted opacity-75 disabled-row\" : \"\"}>\n                                  <td style={{ width: \"25%\" }}>{role.name}</td>\n                                  <td style={{ width: \"25%\" }} className=\"text-center\">\n                                    {role.permission_count}\n                                  </td>\n                                  <td style={{ width: \"25%\" }} className=\"text-center\">\n                                    {new Date(role.updated_ts).toLocaleDateString(\"en-US\", { month: \"2-digit\", day: \"2-digit\", year: \"numeric\" })}\n                                  </td>\n                                  <td style={{ width: \"25%\" }} className=\"text-center\">\n                                    <Button\n                                      className={`clear-btn p-0 text-decoration-underline m-auto ${role.isDefaultRole ? \"text-muted\" : \"color-primary\"}`}\n                                      onClick={() =>\n                                        !role.isDefaultRole &&\n                                        handlePermissionsClick({ id: role.id, name: role.name, isDefaultRole: role.isDefaultRole })\n                                      }\n                                      disabled={role.isDefaultRole}\n                                      style={role.isDefaultRole ? { cursor: \"not-allowed\" } : {}}\n                                    >\n                                      {t(\"edit\")}\n                                    </Button>\n                                  </td>\n                                </tr>\n                              ))\n                          : !isLoadingRolesPermissions && (\n                              <tr>\n                                <td colSpan={5}>\n                                  <p className=\"text-center\">\n                                    <strong>{t(\"no_roles_found\")}</strong>\n                                  </p>\n                                </td>\n                              </tr>\n                            )}\n                      </tbody>\n                    </table>\n                  </InfiniteScroll>\n                </div>\n                // Permissions View End\n              )}\n            </div>\n\n            {activeView === VIEW_MODE.ROLES && (\n              <div className=\"col-md-5\">\n                <Image src={userRolesImg} alt={t(\"user_roles_alt\")} className={styles.user_roles_img} />\n              </div>\n            )}\n          </div>\n        </div>\n      </section>\n\n      {roleModalConfig.show && (\n        <UserRoleModal\n          onClickCancel={closeRoleModal}\n          onSubmitSuccess={handleRoleSuccess}\n          role={roleModalConfig.role}\n          mode={roleModalConfig.mode}\n          disabled={isLoadingRoles}\n        />\n      )}\n\n      {showEditPermissionsModal && selectedRole && (\n        <EditPermissionsModal\n          onClickCancel={() => setShowEditPermissionsModal(false)}\n          onSubmitSuccess={handlePermissionsSuccess}\n          role={selectedRole}\n          disabled={isLoadingRolesPermissions}\n        />\n      )}\n    </>\n  );\n};\n\nexport default RolesPermissions;\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAtBA;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAM,kBAAkB;IAC7B,KAAK;IACL,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,YAAY;IACvB,OAAO;IACP,aAAa;AACf;AAEA,MAAM,mBAAmB;;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAKlD;QAAE,MAAM;QAAO,MAAM,gBAAgB,GAAG;QAAE,MAAM;IAAK;IACxD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IACtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C,UAAU,KAAK;IAExG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;QAAE,OAAO;QAAG,aAAa;IAAE;IAE1G,4EAA4E;IAC5E,MAAM,aAAa,OAAO,gBAAgB,QAAQ,KAAK,EAAE,QAAQ,KAAK;QACpE,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,eAAe,sIAAA,CAAA,gBAAa;YAChE,IAAI,UAAU,MAAM,WAAW,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAChE,MAAM,eAAe,SAAS,IAAI,CAAC,IAAI;gBACvC,SAAS,CAAC,YAAe,QAAQ,eAAe;2BAAI;2BAAc;qBAAa;gBAE/E,IAAI,aAAa,MAAM,GAAG,sIAAA,CAAA,gBAAa,EAAE;oBACvC,WAAW;gBACb,OAAO;oBACL,WAAW;gBACb;gBAEA,WAAW,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO,gBAAgB,aAAa,MAAM;oBAAC,CAAC;YAC/E,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,WAAW;QACb,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,uBAAuB,OAAO,gBAAgB,QAAQ,WAAW,EAAE,QAAgB,QAAQ,KAAK;QACpG,IAAI;YACF,6BAA6B;YAC7B,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,sIAAA,CAAA,gBAAa,EAAE;YACxE,IAAI,SAAS,IAAI,EAAE,SAAS;gBAC1B,MAAM,yBAAyB,SAAS,IAAI,CAAC,IAAI;gBAEjD,mBAAmB,CAAC,YAAe,QAAQ,yBAAyB;2BAAI;2BAAc;qBAAuB;gBAE7G,IAAI,uBAAuB,MAAM,GAAG,sIAAA,CAAA,gBAAa,EAAE;oBACjD,qBAAqB;gBACvB,OAAO;oBACL,qBAAqB;gBACvB;gBAEA,WAAW,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,aAAa,gBAAgB,uBAAuB,MAAM;oBAAC,CAAC;YAC/F,OAAO;gBACL,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO,GAAG,EAAE;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,qBAAqB;YACrB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,6BAA6B;QAC/B;IACF;IAEA,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,eAAe,UAAU,KAAK,EAAE;gBAClC,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,WAAW,GAAG;gBAChB;YACF,OAAO;gBACL,IAAI,gBAAgB,MAAM,KAAK,GAAG;oBAChC,qBAAqB,GAAG,IAAI;gBAC9B;YACF;QACA,4CAA4C;QAC9C;qCAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAC9B,CAAC,MAA8D,OAAoE,IAAI;YACrI,mBAAmB;gBACjB,MAAM;gBACN;gBACA;YACF;QACF;sDACA,EAAE;IAGJ,MAAM,iBAAiB;QACrB,mBAAmB;YACjB,MAAM;YACN,MAAM,gBAAgB,GAAG;YACzB,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,CAAC,SAAkB;QAC3C,IAAI,CAAC,cAAc;YACjB;QACF;QAEA,0CAA0C;QAC1C,MAAM,WAAW;QAEjB,qDAAqD;QACrD,IAAI,gBAAgB,IAAI,KAAK,gBAAgB,GAAG,IAAI,UAAU;YAC5D,gDAAgD;YAChD,SAAS,CAAC,YAAc;oBACtB;wBACE,IAAI,SAAS,EAAE;wBACf,MAAM,SAAS,IAAI;wBACnB,eAAe;oBACjB;uBACG;iBACJ;QACH,OAAO,IAAI,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,IAAI,UAAU;YACpE,uCAAuC;YACvC,SAAS,CAAC,YAAc,UAAU,GAAG,CAAC,CAAC,OAAU,KAAK,EAAE,KAAK,SAAS,EAAE,GAAG;wBAAE,GAAG,IAAI;wBAAE,MAAM,SAAS,IAAI;oBAAC,IAAI;QAChH,OAAO,IAAI,gBAAgB,IAAI,KAAK,gBAAgB,MAAM,IAAI,UAAU;YACtE,wCAAwC;YACxC,SAAS,CAAC,YAAc,UAAU,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK,SAAS,EAAE;QAC5E;QAEA,IAAI,SAAS;YACX,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;QACtB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,cAAc,gBAAgB,IAAI,EAAE;IACtC;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc,gBAAgB,MAAM,EAAE;IACxC;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;QAChB,4BAA4B;IAC9B;IAEA,MAAM,2BAA2B,CAAC,SAAkB;QAClD,IAAI,aAAa;YACf,0EAA0E;YAC1E,mBAAmB,CAAC,YAClB,UAAU,GAAG,CAAC,CAAC,OACb,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;wBACE,GAAG,IAAI;wBACP,MAAM,YAAY,IAAI;wBACtB,kBAAkB,YAAY,gBAAgB;wBAC9C,YAAY,YAAY,UAAU;wBAClC,eAAe,CAAC,CAAC,YAAY,aAAa;oBAC5C,IACA;QAGV,OAAO;YACL,gEAAgE;YAChE,IAAI,eAAe,UAAU,KAAK,EAAE;gBAClC,WAAW,QAAQ,KAAK;YAC1B,OAAO;gBACL,qBAAqB,QAAQ,WAAW,EAAE;YAC5C;QACF;QAEA,IAAI,SAAS;YACX,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE;QACtB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,eAAe,MAAM,IAAI;QAC/B,cAAc;QACd,mBAAmB,EAAE,GAAG,uCAAuC;QAC/D,qBAAqB;QACrB,WAAW,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAE,CAAC;QACjD,qBAAqB,GAAG,cAAc;IACxC;IAEA,MAAM,mCAAmC,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD,EAAE,yBAAyB;IAE3E,qBACE;;0BACE,6LAAC;gBAAQ,WAAW,kKAAA,CAAA,UAAM,CAAC,iBAAiB;0BAC1C,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,eAAe,UAAU,KAAK,GAAG,aAAa;;kDAC5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;oDACE,EAAE,qBAAqB;wDAAE,cAAc;oDAAS;oDAAG;kEAAC,6LAAC;kEAAM,EAAE,cAAc;4DAAE,cAAc;wDAAa;;;;;;;;;;;;;;;;;;;;;;kDAI/G,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+IAAA,CAAA,UAAM;wDACL,WAAW,GAAG,eAAe,UAAU,KAAK,GAAG,gBAAgB,mBAAmB,8BAA8B,CAAC;wDACjH,SAAS,IAAM,cAAc,UAAU,KAAK;kEAE3C,EAAE,cAAc;4DAAE,cAAc;wDAAa;;;;;;kEAEhD,6LAAC,+IAAA,CAAA,UAAM;wDACL,WAAW,GAAG,eAAe,UAAU,WAAW,GAAG,gBAAgB,mBAAmB,qBAAqB,CAAC;wDAC9G,SAAS,IAAM,cAAc,UAAU,WAAW;kEAEjD,EAAE,oBAAoB;4DAAE,cAAc;wDAAmB;;;;;;;;;;;;4CAI7D,eAAe,UAAU,WAAW,kBACnC,6LAAC,qJAAA,CAAA,UAAY;gDAAC,WAAU;0DACtB,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gJAAA,CAAA,UAAO;wDACN,WAAU;wDACV,SAAS;wDACT,MAAK;wDACL,MAAK;wDACL,aAAa,EAAE,oBAAoB;4DAAE,cAAc;wDAAyB;wDAC5E,UAAU,CAAC,IAAM,iCAAiC,EAAE,MAAM,CAAC,KAAK;kEAEhE,cAAA,6LAAC,qJAAA,CAAA,UAAY,CAAC,IAAI;sEAChB,cAAA,6LAAC,oJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQtB,eAAe,UAAU,KAAK,GAC7B,aAAa;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAI,EAAE;;;;;;kEACP,6LAAC,+IAAA,CAAA,UAAM;wDAAC,WAAU;wDAA8B,SAAS,IAAM,cAAc,gBAAgB,GAAG;kEAC7F,EAAE;;;;;;;;;;;;0DAGP,6LAAC;gDAAI,WAAU;0DAEX,cAAA,6LAAC,kLAAA,CAAA,UAAc;oDACb,YAAY,MAAM,MAAM;oDACxB,MAAM,IAAM,WAAW,QAAQ,KAAK;oDACpC,SAAS;oDACT,QAAQ,OAAO,WAAW,GAAG;oDAC7B,WAAU;oDACV,QACE,gCACE,6LAAC;wDAAG,WAAW,CAAC,KAAK,EAAE,kKAAA,CAAA,UAAM,CAAC,UAAU,EAAE;kEACxC,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4DAAC,QAAQ;4DAAI,OAAM;4DAAO,OAAO;4DAAG,cAAc;4DAAG,OAAO;gEAAE,QAAQ;4DAAS;;;;;;;;;;;oDAI9F,YACE,CAAC,kBAAkB,MAAM,MAAM,iBAC7B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;sEAAG,EAAE;;;;;;;;;;iEAEN;8DAGN,cAAA,6LAAC;wDAAG,WAAW,kKAAA,CAAA,UAAM,CAAC,UAAU;kEAC7B,MAAM,MAAM,GACT;+DAAI;yDAAM,CACP,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,GAC1C,GAAG,CAAC,CAAC,qBACJ,6LAAC;gEAAiB,WAAW,KAAK,aAAa,GAAG,kKAAA,CAAA,UAAM,CAAC,aAAa,GAAG;;kFACvE,6LAAC;kFAAG,KAAK,IAAI;;;;;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,+IAAA,CAAA,UAAM;gFACL,WAAW,CAAC,cAAc,EAAE,KAAK,aAAa,GAAG,aAAa,IAAI;gFAClE,SAAS,IAAM,CAAC,KAAK,aAAa,IAAI,gBAAgB;gFACtD,UAAU,KAAK,aAAa;0FAE5B,cAAA,6LAAC,kJAAA,CAAA,UAAQ;;;;;;;;;;0FAEX,6LAAC,+IAAA,CAAA,UAAM;gFACL,WAAW,CAAC,cAAc,EAAE,KAAK,aAAa,GAAG,aAAa,IAAI;gFAClE,SAAS,IAAM,CAAC,KAAK,aAAa,IAAI,kBAAkB;gFACxD,UAAU,KAAK,aAAa;0FAE5B,cAAA,6LAAC,oJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;+DAfR,KAAK,EAAE;;;;wEAoBpB,CAAC,gCACC,6LAAC;4DAAE,WAAU;sEACX,cAAA,6LAAC;0EAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAS7B,iBAAiB;oCACjB,mBAAmB;kDACnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kLAAA,CAAA,UAAc;4CACb,YAAY,gBAAgB,MAAM;4CAClC,MAAM,IAAM,qBAAqB,QAAQ,WAAW,EAAE;4CACtD,SAAS;4CACT,QAAQ,OAAO,WAAW,GAAG;4CAC7B,QACE,2CACE,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,4JAAA,CAAA,UAAa;oDAAC,MAAM;oDAAI,MAAM;oDAAG,WAAU;;;;;;;;;;;4CAIlD,YACE,CAAC,6BAA6B,gBAAgB,MAAM,iBAClD,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;8DACC,cAAA,6LAAC;kEACC,cAAA,6LAAC;4DAAG,SAAS;4DAAG,OAAO;gEAAE,WAAW;gEAAU,iBAAiB;4DAAO;sEACnE,EAAE;;;;;;;;;;;;;;;;;;;;yDAKT;sDAGN,cAAA,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;kEACC,cAAA,6LAAC;;8EACC,6LAAC;oEAAG,OAAO;wEAAE,OAAO;oEAAM;8EAAI,EAAE;;;;;;8EAChC,6LAAC;oEAAG,OAAO;wEAAE,OAAO;oEAAM;oEAAG,WAAU;8EACpC,EAAE;;;;;;8EAEL,6LAAC;oEAAG,OAAO;wEAAE,OAAO;oEAAM;oEAAG,WAAU;8EACpC,EAAE;;;;;;8EAEL,6LAAC;oEAAG,OAAO;wEAAE,OAAO;oEAAM;oEAAG,WAAU;8EACpC,EAAE;;;;;;;;;;;;;;;;;kEAIT,6LAAC;kEACE,gBAAgB,MAAM,GACnB;+DAAI;yDAAgB,CACjB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,GAC1C,GAAG,CAAC,CAAC,qBACJ,6LAAC;gEAAiB,WAAW,KAAK,aAAa,GAAG,uCAAuC;;kFACvF,6LAAC;wEAAG,OAAO;4EAAE,OAAO;wEAAM;kFAAI,KAAK,IAAI;;;;;;kFACvC,6LAAC;wEAAG,OAAO;4EAAE,OAAO;wEAAM;wEAAG,WAAU;kFACpC,KAAK,gBAAgB;;;;;;kFAExB,6LAAC;wEAAG,OAAO;4EAAE,OAAO;wEAAM;wEAAG,WAAU;kFACpC,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,SAAS;4EAAE,OAAO;4EAAW,KAAK;4EAAW,MAAM;wEAAU;;;;;;kFAE7G,6LAAC;wEAAG,OAAO;4EAAE,OAAO;wEAAM;wEAAG,WAAU;kFACrC,cAAA,6LAAC,+IAAA,CAAA,UAAM;4EACL,WAAW,CAAC,+CAA+C,EAAE,KAAK,aAAa,GAAG,eAAe,iBAAiB;4EAClH,SAAS,IACP,CAAC,KAAK,aAAa,IACnB,uBAAuB;oFAAE,IAAI,KAAK,EAAE;oFAAE,MAAM,KAAK,IAAI;oFAAE,eAAe,KAAK,aAAa;gFAAC;4EAE3F,UAAU,KAAK,aAAa;4EAC5B,OAAO,KAAK,aAAa,GAAG;gFAAE,QAAQ;4EAAc,IAAI,CAAC;sFAExD,EAAE;;;;;;;;;;;;+DAlBA,KAAK,EAAE;;;;wEAuBpB,CAAC,2CACC,6LAAC;sEACC,cAAA,6LAAC;gEAAG,SAAS;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EACX,cAAA,6LAAC;kFAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAalC,eAAe,UAAU,KAAK,kBAC7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qUAAA,CAAA,UAAY;oCAAE,KAAK,EAAE;oCAAmB,WAAW,kKAAA,CAAA,UAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO7F,gBAAgB,IAAI,kBACnB,6LAAC,sJAAA,CAAA,UAAa;gBACZ,eAAe;gBACf,iBAAiB;gBACjB,MAAM,gBAAgB,IAAI;gBAC1B,MAAM,gBAAgB,IAAI;gBAC1B,UAAU;;;;;;YAIb,4BAA4B,8BAC3B,6LAAC,6JAAA,CAAA,UAAoB;gBACnB,eAAe,IAAM,4BAA4B;gBACjD,iBAAiB;gBACjB,MAAM;gBACN,UAAU;;;;;;;;AAKpB;GA/aM;;QACM,yMAAA,CAAA,kBAAe;QACL,iKAAA,CAAA,UAAO;;;KAFvB;uCAibS", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/UserRoleModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, useState, useEffect } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textbox from \"../formElements/Textbox\";\nimport { useForm, useWatch } from \"react-hook-form\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { addUserRole, updateUserRole, deleteUserRole } from \"@/services/roleService\";\nimport { roleValidationSchema } from \"@/utils/validationSchema\";\nimport { RoleFormData, RoleModalProps } from \"@/interfaces/roleInterface\";\nimport { UserRoleForm } from \"@/interfaces/employeeInterface\";\nimport Loader from \"../loader/Loader\";\nimport { ROLE_ALTER_MODE } from \"../views/accessManagement/RolesPermissions\";\nimport { useTranslations } from \"next-intl\";\nimport { normalizeSpaces } from \"@/utils/helper\";\n\nconst UserRoleModal: FC<RoleModalProps> = ({ onClickCancel, onSubmitSuccess, disabled, role, mode }) => {\n  const t = useTranslations();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitError, setSubmitError] = useState<string | null>(null);\n  const [isNameChanged, setIsNameChanged] = useState(false);\n\n  const {\n    control,\n    handleSubmit,\n    formState: { errors, isValid },\n  } = useForm<RoleFormData>({\n    defaultValues: {\n      name: role?.name || \"\",\n    },\n    resolver: yupResolver(roleValidationSchema(t)),\n    mode: \"onChange\", // Add onChange validation mode\n  });\n\n  // Watch for changes in the name field\n  const currentName = useWatch({\n    control,\n    name: \"name\",\n    defaultValue: role?.name || \"\",\n  });\n\n  // Update isNameChanged when the name changes\n  useEffect(() => {\n    if (mode === ROLE_ALTER_MODE.EDIT && role) {\n      const normalizedCurrentName = normalizeSpaces(currentName);\n      const normalizedOriginalName = normalizeSpaces(role.name);\n      setIsNameChanged(normalizedCurrentName !== normalizedOriginalName && normalizedCurrentName !== \"\");\n    }\n  }, [currentName, role, mode]);\n\n  const onSubmit = async (data: RoleFormData) => {\n    try {\n      setIsSubmitting(true);\n      setSubmitError(null);\n\n      const requestData: UserRoleForm = {\n        name: normalizeSpaces(data.name),\n      };\n      let response;\n      try {\n        if (mode === ROLE_ALTER_MODE.ADD) {\n          response = await addUserRole(requestData);\n        } else if (mode === ROLE_ALTER_MODE.EDIT && role) {\n          response = await updateUserRole(role.id, requestData);\n        } else if (mode === ROLE_ALTER_MODE.DELETE && role) {\n          response = await deleteUserRole(role.id);\n        } else {\n          throw new Error(t(\"unexpected_error\"));\n        }\n\n        const result = response.data;\n\n        if (result && result.success) {\n          // Call onSubmitSuccess with the success message and response data\n          const actionType = mode === ROLE_ALTER_MODE.ADD ? t(\"added\") : mode === ROLE_ALTER_MODE.EDIT ? t(\"updated\") : t(\"deleted\");\n          const successMessage = t(\"role_action_success\", { actionType: actionType });\n          onSubmitSuccess(successMessage, result.data);\n          onClickCancel();\n        } else {\n          const errorMessage = t(result.message || \"failed_role_operation\");\n          setSubmitError(errorMessage);\n        }\n      } catch (error) {\n        console.error(error);\n        const apiError = response?.error;\n        const errorMessage = apiError?.status === 401 ? t(\"authentication_error\") : apiError?.message || t(\"failed_role_operation\");\n\n        setSubmitError(errorMessage);\n      }\n    } catch (error) {\n      console.error(error);\n      setSubmitError(t(\"unexpected_error\"));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const getModalTitle = () => {\n    switch (mode) {\n      case ROLE_ALTER_MODE.ADD:\n        return t(\"add_role\");\n      case ROLE_ALTER_MODE.EDIT:\n        return t(\"edit_role\");\n      case ROLE_ALTER_MODE.DELETE:\n        return t(\"delete_role\");\n      default:\n        return t(\"role\");\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center pb-0\">\n            <h2 className=\"m-0\">{getModalTitle()}</h2>\n\n            <Button className=\"modal-close-btn\" onClick={onClickCancel} disabled={isSubmitting}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body\">\n            {mode === ROLE_ALTER_MODE.DELETE ? (\n              <div>\n                <p className=\"text-center mb-4\">{t(\"confirm_delete_role\", { roleName: role?.name || \"\" })}</p>\n\n                {submitError && (\n                  <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                    {submitError}\n                  </div>\n                )}\n\n                <div className=\"button-align mt-4\">\n                  <Button type=\"button\" className=\"danger-btn rounded-md w-100\" onClick={handleSubmit(onSubmit)} disabled={isSubmitting || disabled}>\n                    <div className=\"d-flex align-items-center justify-content-center\">\n                      {isSubmitting && <Loader />}\n                      <span className={isSubmitting ? \"ms-2\" : \"\"}>{t(\"delete_role\")}</span>\n                    </div>\n                  </Button>\n                  <Button type=\"button\" className=\"dark-outline-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting || disabled}>\n                    {t(\"cancel\")}\n                  </Button>\n                </div>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit(onSubmit)}>\n                <InputWrapper className=\"mb-4\">\n                  <InputWrapper.Label htmlFor=\"name\" required className=\"fw-bold\">\n                    {t(\"role_name\")}\n                  </InputWrapper.Label>\n                  <Textbox\n                    className=\"form-control\"\n                    control={control}\n                    name=\"name\"\n                    type=\"text\"\n                    placeholder={t(\"enter_role_name\")}\n                    disabled={isSubmitting || disabled}\n                  />\n                  <InputWrapper.Error message={errors?.name?.message || \"\"} />\n                </InputWrapper>\n\n                {submitError && (\n                  <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                    {submitError}\n                  </div>\n                )}\n\n                <div className=\"button-align mt-4\">\n                  <Button\n                    type=\"submit\"\n                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || (mode === ROLE_ALTER_MODE.EDIT && !isNameChanged) || !isValid || normalizeSpaces(currentName) === \"\" ? \"truly-disabled\" : \"\"}`}\n                    disabled={\n                      isSubmitting || disabled || (mode === ROLE_ALTER_MODE.EDIT && !isNameChanged) || !isValid || normalizeSpaces(currentName) === \"\"\n                    }\n                    title={\n                      mode === ROLE_ALTER_MODE.EDIT && !isNameChanged\n                        ? t(\"change_role_name_hint\")\n                        : normalizeSpaces(currentName) === \"\"\n                          ? t(\"role_name_req\")\n                          : \"\"\n                    }\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center\">\n                      {isSubmitting && <Loader />}\n                      <span className={isSubmitting ? \"ms-2\" : \"\"}>{getModalTitle()}</span>\n                    </div>\n                  </Button>\n                  <Button type=\"button\" className=\"dark-outline-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting || disabled}>\n                    {t(\"cancel\")}\n                  </Button>\n                </div>\n              </form>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UserRoleModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;AAiBA,MAAM,gBAAoC,CAAC,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;;IACjG,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC/B,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,eAAe;YACb,MAAM,MAAM,QAAQ;QACtB;QACA,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3C,MAAM;IACR;IAEA,sCAAsC;IACtC,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B;QACA,MAAM;QACN,cAAc,MAAM,QAAQ;IAC9B;IAEA,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,MAAM;gBACzC,MAAM,wBAAwB,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;gBAC9C,MAAM,yBAAyB,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;gBACxD,iBAAiB,0BAA0B,0BAA0B,0BAA0B;YACjG;QACF;kCAAG;QAAC;QAAa;QAAM;KAAK;IAE5B,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAChB,eAAe;YAEf,MAAM,cAA4B;gBAChC,MAAM,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;YACjC;YACA,IAAI;YACJ,IAAI;gBACF,IAAI,SAAS,sKAAA,CAAA,kBAAe,CAAC,GAAG,EAAE;oBAChC,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;gBAC/B,OAAO,IAAI,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,MAAM;oBAChD,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE,EAAE;gBAC3C,OAAO,IAAI,SAAS,sKAAA,CAAA,kBAAe,CAAC,MAAM,IAAI,MAAM;oBAClD,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;gBACzC,OAAO;oBACL,MAAM,IAAI,MAAM,EAAE;gBACpB;gBAEA,MAAM,SAAS,SAAS,IAAI;gBAE5B,IAAI,UAAU,OAAO,OAAO,EAAE;oBAC5B,kEAAkE;oBAClE,MAAM,aAAa,SAAS,sKAAA,CAAA,kBAAe,CAAC,GAAG,GAAG,EAAE,WAAW,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,GAAG,EAAE,aAAa,EAAE;oBAChH,MAAM,iBAAiB,EAAE,uBAAuB;wBAAE,YAAY;oBAAW;oBACzE,gBAAgB,gBAAgB,OAAO,IAAI;oBAC3C;gBACF,OAAO;oBACL,MAAM,eAAe,EAAE,OAAO,OAAO,IAAI;oBACzC,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC;gBACd,MAAM,WAAW,UAAU;gBAC3B,MAAM,eAAe,UAAU,WAAW,MAAM,EAAE,0BAA0B,UAAU,WAAW,EAAE;gBAEnG,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,eAAe,EAAE;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK,sKAAA,CAAA,kBAAe,CAAC,GAAG;gBACtB,OAAO,EAAE;YACX,KAAK,sKAAA,CAAA,kBAAe,CAAC,IAAI;gBACvB,OAAO,EAAE;YACX,KAAK,sKAAA,CAAA,kBAAe,CAAC,MAAM;gBACzB,OAAO,EAAE;YACX;gBACE,OAAO,EAAE;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAO;;;;;;0CAErB,6LAAC,+IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAe,UAAU;0CACpE,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;kCACZ,SAAS,sKAAA,CAAA,kBAAe,CAAC,MAAM,iBAC9B,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAoB,EAAE,uBAAuB;wCAAE,UAAU,MAAM,QAAQ;oCAAG;;;;;;gCAEtF,6BACC,6LAAC;oCAAI,WAAU;oCAA0B,MAAK;8CAC3C;;;;;;8CAIL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAA8B,SAAS,aAAa;4CAAW,UAAU,gBAAgB;sDACvH,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,8BAAgB,6LAAC,yIAAA,CAAA,UAAM;;;;;kEACxB,6LAAC;wDAAK,WAAW,eAAe,SAAS;kEAAK,EAAE;;;;;;;;;;;;;;;;;sDAGpD,6LAAC,+IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAoC,SAAS;4CAAe,UAAU,gBAAgB;sDACnH,EAAE;;;;;;;;;;;;;;;;;iDAKT,6LAAC;4BAAK,UAAU,aAAa;;8CAC3B,6LAAC,qJAAA,CAAA,UAAY;oCAAC,WAAU;;sDACtB,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAO,QAAQ;4CAAC,WAAU;sDACnD,EAAE;;;;;;sDAEL,6LAAC,gJAAA,CAAA,UAAO;4CACN,WAAU;4CACV,SAAS;4CACT,MAAK;4CACL,MAAK;4CACL,aAAa,EAAE;4CACf,UAAU,gBAAgB;;;;;;sDAE5B,6LAAC,qJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,QAAQ,MAAM,WAAW;;;;;;;;;;;;gCAGvD,6BACC,6LAAC;oCAAI,WAAU;oCAA0B,MAAK;8CAC3C;;;;;;8CAIL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+IAAA,CAAA,UAAM;4CACL,MAAK;4CACL,WAAW,CAAC,6BAA6B,EAAE,gBAAgB,YAAa,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,CAAC,iBAAkB,CAAC,WAAW,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB,KAAK,mBAAmB,IAAI;4CACrM,UACE,gBAAgB,YAAa,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,CAAC,iBAAkB,CAAC,WAAW,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB;4CAEhI,OACE,SAAS,sKAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,CAAC,gBAC9B,EAAE,2BACF,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB,KAC/B,EAAE,mBACF;sDAGR,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,8BAAgB,6LAAC,yIAAA,CAAA,UAAM;;;;;kEACxB,6LAAC;wDAAK,WAAW,eAAe,SAAS;kEAAK;;;;;;;;;;;;;;;;;sDAGlD,6LAAC,+IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAoC,SAAS;4CAAe,UAAU,gBAAgB;sDACnH,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;GAtLM;;QACM,yMAAA,CAAA,kBAAe;QASrB,iKAAA,CAAA,UAAO;QASS,iKAAA,CAAA,WAAQ;;;KAnBxB;uCAwLS", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/RefreshAlertIcon.tsx"], "sourcesContent": ["const RefreshAlertIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"32\"\n      height=\"32\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      className={className}\n      stroke=\"#000000\"\n      stroke-width=\"1\"\n      stroke-linecap=\"round\"\n      stroke-linejoin=\"round\"\n    >\n      <path d=\"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4\" />\n      <path d=\"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4\" />\n      <path d=\"M12 9l0 3\" />\n      <path d=\"M12 15l.01 0\" />\n    </svg>\n  );\n};\n\nexport default RefreshAlertIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,mBAAmB,CAAC;IACxB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,WAAW;QACX,QAAO;QACP,gBAAa;QACb,kBAAe;QACf,mBAAgB;;0BAEhB,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;AAGd;KArBM;uCAuBS", "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/EditPermissionsModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, useState, useEffect } from \"react\";\nimport Button from \"../formElements/Button\";\nimport { getRolePermissionsById, updateRolePermissions } from \"@/services/roleService\";\nimport { IProps } from \"@/interfaces/rolePermissionInterface\";\nimport Loader from \"../loader/Loader\";\nimport { useTranslations } from \"next-intl\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport Skeleton from \"react-loading-skeleton\";\nimport RefreshAlertIcon from \"../svgComponents/RefreshAlertIcon\";\n\ninterface IPermission {\n  id: number;\n  name: string;\n  description: string;\n  checked: boolean;\n}\n\nconst EditPermissionsModal: FC<IProps> = ({ onClickCancel, onSubmitSuccess, disabled, role }) => {\n  const t = useTranslations();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [submitError, setSubmitError] = useState<string | null>(null);\n  const [loadError, setLoadError] = useState<string | null>(null);\n  const [permissions, setPermissions] = useState<IPermission[]>([]);\n  const [originalPermissions, setOriginalPermissions] = useState<IPermission[]>([]);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [hasAtLeastOneSelected, setHasAtLeastOneSelected] = useState(false);\n  const [roleName, setRoleName] = useState(role.name);\n\n  // Fetch permissions from API when component mounts\n  useEffect(() => {\n    const fetchPermissions = async () => {\n      try {\n        setIsLoading(true);\n        setLoadError(null);\n\n        const response = await getRolePermissionsById(role.id);\n        const data = response.data;\n\n        if (data.success && data.data) {\n          setRoleName(data.data.role_name);\n          // Transform API response to component state format\n          const transformedPermissions = data.data.permissions.map((permission) => ({\n            id: permission.id,\n            name: permission.name,\n            description: permission.description,\n            checked: permission.selected || false,\n          }));\n          setPermissions(transformedPermissions);\n          setOriginalPermissions(transformedPermissions);\n\n          // Check if at least one permission is selected\n          const hasSelected = transformedPermissions.some((p) => p.checked);\n          setHasAtLeastOneSelected(hasSelected);\n        } else {\n          console.error(t(\"failed_load_permissions\"));\n          setLoadError(t(\"failed_load_permissions\"));\n        }\n      } catch (error) {\n        console.error(error);\n        setLoadError(t(\"unexpected_error\"));\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchPermissions();\n  }, [role.id]);\n\n  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const isChecked = e.target.checked;\n    const updatedPermissions = permissions.map((permission) => ({\n      ...permission,\n      checked: isChecked,\n    }));\n    setPermissions(updatedPermissions);\n\n    // Check if permissions have changed from original state\n    const hasAnyChanges = updatedPermissions.some((updatedPerm, index) => updatedPerm.checked !== originalPermissions[index].checked);\n    setHasChanges(hasAnyChanges);\n\n    // Update whether at least one permission is selected\n    setHasAtLeastOneSelected(isChecked);\n  };\n\n  const handlePermissionChange = (id: number, checked: boolean) => {\n    const updatedPermissions = permissions.map((permission) => (permission.id === id ? { ...permission, checked } : permission));\n    setPermissions(updatedPermissions);\n\n    // Check if any permission has changed from its original state\n    const hasAnyChanges = updatedPermissions.some((updatedPerm) => {\n      const originalPerm = originalPermissions.find((p) => p.id === updatedPerm.id);\n      return originalPerm && originalPerm.checked !== updatedPerm.checked;\n    });\n    setHasChanges(hasAnyChanges);\n\n    // Check if at least one permission is selected\n    const hasSelected = updatedPermissions.some((p) => p.checked);\n    setHasAtLeastOneSelected(hasSelected);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      setIsSubmitting(true);\n      setSubmitError(null);\n\n      // Get only the IDs of checked permissions\n      const permissionIds = permissions.reduce((acc: number[], p) => {\n        if (p.checked) acc.push(p.id);\n        return acc;\n      }, []);\n\n      // Make the API call to update role permissions with only permissionIds\n      const response = await updateRolePermissions(role.id, permissionIds);\n      const data = response.data;\n      if (data.success) {\n        // Call the success callback with success message and updated role data\n        onSubmitSuccess(t(\"permissions_updated_success\"), data.data);\n        // Close the modal\n        onClickCancel();\n      } else {\n        console.error(data.message);\n        setSubmitError(data.message || t(\"failed_update_permissions\"));\n      }\n    } catch (error) {\n      console.error(error);\n      setSubmitError(t(\"unexpected_error\"));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered modal-lg\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center pb-0\">\n            <h4 className=\"m-0\">\n              {t(\"edit_permissions_for\")} <span>{roleName}</span>\n            </h4>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel} disabled={isSubmitting}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body pt-3\">\n            <p className=\"mb-5 text-center px-3\">{t(\"permissions_description\")}</p>\n            {isLoading ? (\n              <div className=\"permissions-card p-5 w-100 mb-4\">\n                <Skeleton height={20} width=\"100%\" borderRadius={4} count={8} className=\"mb-4\" />\n              </div>\n            ) : loadError ? (\n              <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                {loadError}\n                <Button\n                  className=\"clear-btn p-0\"\n                  onClick={() => {\n                    setIsLoading(true);\n                    getRolePermissionsById(role.id)\n                      .then((response) => {\n                        const data = response.data;\n                        if (data.success && data.data) {\n                          setRoleName(data.data.role_name);\n                          const transformedPermissions = data.data.permissions.map((permission) => ({\n                            id: permission.id,\n                            name: permission.name,\n                            description: permission.description,\n                            checked: permission.selected || false,\n                          }));\n                          setPermissions(transformedPermissions);\n                          setLoadError(null);\n                        } else {\n                          setLoadError(t(\"failed_load_permissions\"));\n                        }\n                      })\n                      .catch((error) => {\n                        console.error(\"Error fetching permissions:\", error);\n                        setLoadError(t(\"unexpected_error\"));\n                      })\n                      .finally(() => setIsLoading(false));\n                  }}\n                >\n                  <RefreshAlertIcon />\n                </Button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"permissions-card p-5\">\n                  <h3>{t(\"role_permissions\")}</h3>\n                  {permissions.length > 0 ? (\n                    <>\n                      <label className=\"container-checkbox\">\n                        {t(\"select_all\")}\n                        <input\n                          type=\"checkbox\"\n                          onChange={handleSelectAll}\n                          checked={permissions.length > 0 && permissions.every((p) => p.checked)}\n                          disabled={isSubmitting || disabled}\n                        />\n                        <span className=\"checkmark\" />\n                      </label>\n                      <ul className=\"checbox-group\">\n                        {permissions.map((permission) => (\n                          <li key={permission.id}>\n                            <label className=\"container-checkbox\">\n                              <div className=\"permission-item\">\n                                <div className=\"permission-name\">{permission.name}</div>\n                                <div className=\"permission-description\">{permission.description}</div>\n                              </div>\n                              <input\n                                type=\"checkbox\"\n                                checked={permission.checked}\n                                onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}\n                                disabled={isSubmitting || disabled}\n                              />\n                              <span className=\"checkmark\" />\n                            </label>\n                          </li>\n                        ))}\n                      </ul>\n                    </>\n                  ) : (\n                    <p className=\"text-center py-3\">{t(\"no_permissions_found\")}</p>\n                  )}\n                </div>\n\n                {submitError && (\n                  <div className=\"alert alert-danger mb-3\" role=\"alert\">\n                    {submitError}\n                  </div>\n                )}\n\n                <div className=\"button-align mt-5\">\n                  <Button type=\"button\" className=\"dark-outline-btn rounded-md w-100\" onClick={onClickCancel} disabled={isSubmitting || disabled}>\n                    {t(\"cancel_department_edit\")}\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected ? \"truly-disabled\" : \"\"}`}\n                    disabled={isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected}\n                    title={!hasChanges ? t(\"make_changes_to_enable_save\") : !hasAtLeastOneSelected ? t(\"at_least_one_permission\") : \"\"}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <Loader /> <span className=\"ms-2\">{t(\"saving\")}</span>\n                      </>\n                    ) : (\n                      t(\"save_permissions\")\n                    )}\n                  </Button>\n                </div>\n              </form>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EditPermissionsModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAkBA,MAAM,uBAAmC,CAAC,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE;;IAC1F,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI;IAElD,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;mEAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,aAAa;wBAEb,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;wBACrD,MAAM,OAAO,SAAS,IAAI;wBAE1B,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;4BAC7B,YAAY,KAAK,IAAI,CAAC,SAAS;4BAC/B,mDAAmD;4BACnD,MAAM,yBAAyB,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG;0GAAC,CAAC,aAAe,CAAC;wCACxE,IAAI,WAAW,EAAE;wCACjB,MAAM,WAAW,IAAI;wCACrB,aAAa,WAAW,WAAW;wCACnC,SAAS,WAAW,QAAQ,IAAI;oCAClC,CAAC;;4BACD,eAAe;4BACf,uBAAuB;4BAEvB,+CAA+C;4BAC/C,MAAM,cAAc,uBAAuB,IAAI;+FAAC,CAAC,IAAM,EAAE,OAAO;;4BAChE,yBAAyB;wBAC3B,OAAO;4BACL,QAAQ,KAAK,CAAC,EAAE;4BAChB,aAAa,EAAE;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC;wBACd,aAAa,EAAE;oBACjB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;yCAAG;QAAC,KAAK,EAAE;KAAC;IAEZ,MAAM,kBAAkB,CAAC;QACvB,MAAM,YAAY,EAAE,MAAM,CAAC,OAAO;QAClC,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,aAAe,CAAC;gBAC1D,GAAG,UAAU;gBACb,SAAS;YACX,CAAC;QACD,eAAe;QAEf,wDAAwD;QACxD,MAAM,gBAAgB,mBAAmB,IAAI,CAAC,CAAC,aAAa,QAAU,YAAY,OAAO,KAAK,mBAAmB,CAAC,MAAM,CAAC,OAAO;QAChI,cAAc;QAEd,qDAAqD;QACrD,yBAAyB;IAC3B;IAEA,MAAM,yBAAyB,CAAC,IAAY;QAC1C,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,aAAgB,WAAW,EAAE,KAAK,KAAK;gBAAE,GAAG,UAAU;gBAAE;YAAQ,IAAI;QAChH,eAAe;QAEf,8DAA8D;QAC9D,MAAM,gBAAgB,mBAAmB,IAAI,CAAC,CAAC;YAC7C,MAAM,eAAe,oBAAoB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,YAAY,EAAE;YAC5E,OAAO,gBAAgB,aAAa,OAAO,KAAK,YAAY,OAAO;QACrE;QACA,cAAc;QAEd,+CAA+C;QAC/C,MAAM,cAAc,mBAAmB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QAC5D,yBAAyB;IAC3B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,gBAAgB;YAChB,eAAe;YAEf,0CAA0C;YAC1C,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,KAAe;gBACvD,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;gBAC5B,OAAO;YACT,GAAG,EAAE;YAEL,uEAAuE;YACvE,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,OAAO,SAAS,IAAI;YAC1B,IAAI,KAAK,OAAO,EAAE;gBAChB,uEAAuE;gBACvE,gBAAgB,EAAE,gCAAgC,KAAK,IAAI;gBAC3D,kBAAkB;gBAClB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,KAAK,OAAO;gBAC1B,eAAe,KAAK,OAAO,IAAI,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,eAAe,EAAE;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,EAAE;oCAAwB;kDAAC,6LAAC;kDAAM;;;;;;;;;;;;0CAErC,6LAAC,+IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAe,UAAU;0CACpE,cAAA,6LAAC,wJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAyB,EAAE;;;;;;4BACvC,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;oCAAC,QAAQ;oCAAI,OAAM;oCAAO,cAAc;oCAAG,OAAO;oCAAG,WAAU;;;;;;;;;;uCAExE,0BACF,6LAAC;gCAAI,WAAU;gCAA0B,MAAK;;oCAC3C;kDACD,6LAAC,+IAAA,CAAA,UAAM;wCACL,WAAU;wCACV,SAAS;4CACP,aAAa;4CACb,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE,EAC3B,IAAI,CAAC,CAAC;gDACL,MAAM,OAAO,SAAS,IAAI;gDAC1B,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;oDAC7B,YAAY,KAAK,IAAI,CAAC,SAAS;oDAC/B,MAAM,yBAAyB,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,aAAe,CAAC;4DACxE,IAAI,WAAW,EAAE;4DACjB,MAAM,WAAW,IAAI;4DACrB,aAAa,WAAW,WAAW;4DACnC,SAAS,WAAW,QAAQ,IAAI;wDAClC,CAAC;oDACD,eAAe;oDACf,aAAa;gDACf,OAAO;oDACL,aAAa,EAAE;gDACjB;4CACF,GACC,KAAK,CAAC,CAAC;gDACN,QAAQ,KAAK,CAAC,+BAA+B;gDAC7C,aAAa,EAAE;4CACjB,GACC,OAAO,CAAC,IAAM,aAAa;wCAChC;kDAEA,cAAA,6LAAC,0JAAA,CAAA,UAAgB;;;;;;;;;;;;;;;qDAIrB,6LAAC;gCAAK,UAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAI,EAAE;;;;;;4CACN,YAAY,MAAM,GAAG,kBACpB;;kEACE,6LAAC;wDAAM,WAAU;;4DACd,EAAE;0EACH,6LAAC;gEACC,MAAK;gEACL,UAAU;gEACV,SAAS,YAAY,MAAM,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,IAAM,EAAE,OAAO;gEACrE,UAAU,gBAAgB;;;;;;0EAE5B,6LAAC;gEAAK,WAAU;;;;;;;;;;;;kEAElB,6LAAC;wDAAG,WAAU;kEACX,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;0EACC,cAAA,6LAAC;oEAAM,WAAU;;sFACf,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAmB,WAAW,IAAI;;;;;;8FACjD,6LAAC;oFAAI,WAAU;8FAA0B,WAAW,WAAW;;;;;;;;;;;;sFAEjE,6LAAC;4EACC,MAAK;4EACL,SAAS,WAAW,OAAO;4EAC3B,UAAU,CAAC,IAAM,uBAAuB,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO;4EACvE,UAAU,gBAAgB;;;;;;sFAE5B,6LAAC;4EAAK,WAAU;;;;;;;;;;;;+DAZX,WAAW,EAAE;;;;;;;;;;;6EAmB5B,6LAAC;gDAAE,WAAU;0DAAoB,EAAE;;;;;;;;;;;;oCAItC,6BACC,6LAAC;wCAAI,WAAU;wCAA0B,MAAK;kDAC3C;;;;;;kDAIL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+IAAA,CAAA,UAAM;gDAAC,MAAK;gDAAS,WAAU;gDAAoC,SAAS;gDAAe,UAAU,gBAAgB;0DACnH,EAAE;;;;;;0DAEL,6LAAC,+IAAA,CAAA,UAAM;gDACL,MAAK;gDACL,WAAW,CAAC,6BAA6B,EAAE,gBAAgB,YAAY,YAAY,MAAM,KAAK,KAAK,CAAC,cAAc,CAAC,wBAAwB,mBAAmB,IAAI;gDAClK,UAAU,gBAAgB,YAAY,YAAY,MAAM,KAAK,KAAK,CAAC,cAAc,CAAC;gDAClF,OAAO,CAAC,aAAa,EAAE,iCAAiC,CAAC,wBAAwB,EAAE,6BAA6B;0DAE/G,6BACC;;sEACE,6LAAC,yIAAA,CAAA,UAAM;;;;;wDAAG;sEAAC,6LAAC;4DAAK,WAAU;sEAAQ,EAAE;;;;;;;mEAGvC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB;GAjPM;;QACM,yMAAA,CAAA,kBAAe;;;KADrB;uCAmPS", "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/skeletons/TableSkeleton.tsx"], "sourcesContent": ["import React from \"react\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\nconst TableSkeleton = ({ rows = 3, cols = 3, colWidths = \"120,80,100\" }) => {\n  const columnWidths = colWidths.split(\",\").map((w) => w.trim());\n\n  return (\n    <tbody>\n      {[...Array(rows)].map((_, rowIndex) => (\n        <tr key={`loader-row-${rowIndex}`}>\n          {[...Array(cols)].map((_, colIndex) => (\n            <td key={`loader-col-${colIndex}`} className=\"text-center\">\n              <Skeleton width={columnWidths[colIndex] || 80} height={20} circle={false} />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </tbody>\n  );\n};\n\nexport default TableSkeleton;\n"], "names": [], "mappings": ";;;;AACA;;;;AAGA,MAAM,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,YAAY,YAAY,EAAE;IACrE,MAAM,eAAe,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAE3D,qBACE,6LAAC;kBACE;eAAI,MAAM;SAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,6LAAC;0BACE;uBAAI,MAAM;iBAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,6LAAC;wBAAkC,WAAU;kCAC3C,cAAA,6LAAC,gKAAA,CAAA,UAAQ;4BAAC,OAAO,YAAY,CAAC,SAAS,IAAI;4BAAI,QAAQ;4BAAI,QAAQ;;;;;;uBAD5D,CAAC,WAAW,EAAE,UAAU;;;;;eAF5B,CAAC,WAAW,EAAE,UAAU;;;;;;;;;;AAUzC;KAhBM;uCAkBS", "debugId": null}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}