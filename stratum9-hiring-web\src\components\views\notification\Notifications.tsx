/* eslint-disable react-hooks/exhaustive-deps */
import Button from "@/components/formElements/Button";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import { setHasUnreadNotification, setNotificationsData } from "@/redux/slices/notificationSlice";
import { getNotifications, deleteAllNotifications, updateNotificationStatus } from "@/services/notificationServices/notificationService";
import { toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";

interface NotificationsProps {
  setIsNotificationOpen: (value: boolean) => void;
}

const Notifications = ({ setIsNotificationOpen }: NotificationsProps) => {
  const t = useTranslations();
  const tCommon = useTranslations("common");
  const [loading, setLoading] = useState<boolean>(true);
  const [offset, setOffset] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const notificationRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { notifications } = useSelector((state: RootState) => state.notification);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node) &&
        (event.target as HTMLElement).id !== "notification-icon-id"
      ) {
        setIsNotificationOpen(false);
        dispatch(setHasUnreadNotification(false));
        dispatch(setNotificationsData(notifications.map((item) => ({ ...item, isWatched: 1 }))));
        updateNotificationStatus();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    dispatch(setNotificationsData([]));
    fetchNotifications(offset);
  }, []);

  const fetchNotifications = async (offset: number) => {
    try {
      setLoading(true);
      const response = await getNotifications({ offset, limit: DEFAULT_LIMIT });

      if (response.data?.success) {
        console.log("Notifications fetched successfully:", response.data.data);

        dispatch(setNotificationsData([...notifications, ...response.data.data]));
        setOffset((prevOffset) => prevOffset + DEFAULT_LIMIT);
        setHasMore(response.data.data.length === DEFAULT_LIMIT);
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch (error) {
      console.log(error);

      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleClearAll = async () => {
    try {
      setLoading(true);
      const res = await deleteAllNotifications();

      if (res.data?.success) {
        dispatch(setNotificationsData([])); // Clear UI
      } else {
        toastMessageError(t("failed_to_delete_notifications"));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="notifications" ref={notificationRef}>
      <div className="header-content">
        <h3>{tCommon("notifications")}</h3>
        <Button onClick={notifications.length === 0 ? handleClearAll : undefined} className="clear-btn p-0">
          {tCommon("clear_all")}
        </Button>
      </div>

      <div className="read-btns">{/* Future filter buttons */}</div>

      <div className="notification-wrapper" id="notification-scroll-container">
        <InfiniteScroll
          dataLength={notifications.length}
          next={() => fetchNotifications(offset)}
          hasMore={hasMore}
          loader={<p style={{ padding: "1rem" }}>Loading...</p>}
          scrollableTarget="notification-scroll-container"
          endMessage={
            notifications.length > 0 && (
              <p style={{ padding: "1rem", textAlign: "center" }}>
                <b>{tCommon("no_more_notifications")}</b>
              </p>
            )
          }
        >
          {notifications.length === 0 && !loading ? (
            <p style={{ padding: "1rem" }}>{tCommon("no_notifications_found")}</p>
          ) : (
            notifications.map((item) => (
              <div key={item.id} className={`notification-item ${item.isWatched === 0 ? "unread" : ""}`}>
                <h4 style={{ margin: 0 }}>{item.title}</h4>
                <p>{item.description}</p>
                <p className="time">
                  {new Date(item.createdTs).toLocaleString("en-IN", {
                    dateStyle: "medium",
                    timeStyle: "short",
                  })}
                </p>
              </div>
            ))
          )}
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default React.memo(Notifications);
