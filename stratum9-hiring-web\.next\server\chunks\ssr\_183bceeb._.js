module.exports = {

"[project]/src/styles/accessManagement.module.scss.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "access_management": "accessManagement-module-scss-module__AuP3Pq__access_management",
  "active": "accessManagement-module-scss-module__AuP3Pq__active",
  "add_employee_height": "accessManagement-module-scss-module__AuP3Pq__add_employee_height",
  "benefit_text": "accessManagement-module-scss-module__AuP3Pq__benefit_text",
  "checkmark": "accessManagement-module-scss-module__AuP3Pq__checkmark",
  "custom_dropdown": "accessManagement-module-scss-module__AuP3Pq__custom_dropdown",
  "department_card": "accessManagement-module-scss-module__AuP3Pq__department_card",
  "description_cell": "accessManagement-module-scss-module__AuP3Pq__description_cell",
  "disabled": "accessManagement-module-scss-module__AuP3Pq__disabled",
  "disabled_role": "accessManagement-module-scss-module__AuP3Pq__disabled_role",
  "dropdown_arrow": "accessManagement-module-scss-module__AuP3Pq__dropdown_arrow",
  "dropdown_divider": "accessManagement-module-scss-module__AuP3Pq__dropdown_divider",
  "dropdown_item": "accessManagement-module-scss-module__AuP3Pq__dropdown_item",
  "fadeIn": "accessManagement-module-scss-module__AuP3Pq__fadeIn",
  "fixed_height": "accessManagement-module-scss-module__AuP3Pq__fixed_height",
  "flex_content": "accessManagement-module-scss-module__AuP3Pq__flex_content",
  "folder_container": "accessManagement-module-scss-module__AuP3Pq__folder_container",
  "form_card": "accessManagement-module-scss-module__AuP3Pq__form_card",
  "plan_name": "accessManagement-module-scss-module__AuP3Pq__plan_name",
  "plan_option": "accessManagement-module-scss-module__AuP3Pq__plan_option",
  "plan_price": "accessManagement-module-scss-module__AuP3Pq__plan_price",
  "price_type": "accessManagement-module-scss-module__AuP3Pq__price_type",
  "role_dropdown": "accessManagement-module-scss-module__AuP3Pq__role_dropdown",
  "role_option": "accessManagement-module-scss-module__AuP3Pq__role_option",
  "role_select": "accessManagement-module-scss-module__AuP3Pq__role_select",
  "role_select_employee": "accessManagement-module-scss-module__AuP3Pq__role_select_employee",
  "role_selector": "accessManagement-module-scss-module__AuP3Pq__role_selector",
  "save_badge": "accessManagement-module-scss-module__AuP3Pq__save_badge",
  "section_title": "accessManagement-module-scss-module__AuP3Pq__section_title",
  "select_plan": "accessManagement-module-scss-module__AuP3Pq__select_plan",
  "select_plan_section": "accessManagement-module-scss-module__AuP3Pq__select_plan_section",
  "selected": "accessManagement-module-scss-module__AuP3Pq__selected",
  "selected_plan": "accessManagement-module-scss-module__AuP3Pq__selected_plan",
  "selected_role": "accessManagement-module-scss-module__AuP3Pq__selected_role",
  "show_below": "accessManagement-module-scss-module__AuP3Pq__show_below",
  "sibling_height": "accessManagement-module-scss-module__AuP3Pq__sibling_height",
  "side_bar": "accessManagement-module-scss-module__AuP3Pq__side_bar",
  "subscription_benefit_text": "accessManagement-module-scss-module__AuP3Pq__subscription_benefit_text",
  "subscription_option": "accessManagement-module-scss-module__AuP3Pq__subscription_option",
  "subscription_page": "accessManagement-module-scss-module__AuP3Pq__subscription_page",
  "subscription_plan": "accessManagement-module-scss-module__AuP3Pq__subscription_plan",
  "subscription_plan_card": "accessManagement-module-scss-module__AuP3Pq__subscription_plan_card",
  "tip_para": "accessManagement-module-scss-module__AuP3Pq__tip_para",
  "user_roles": "accessManagement-module-scss-module__AuP3Pq__user_roles",
  "user_roles_img": "accessManagement-module-scss-module__AuP3Pq__user_roles_img",
});
}}),
"[project]/src/components/svgComponents/EditIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function EditIcon({ className, fillNone, fillColor }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 27 26",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M25.6677 25.2539H15.9971C15.4451 25.2539 14.9971 24.8059 14.9971 24.2539C14.9971 23.7019 15.4451 23.2539 15.9971 23.2539H25.6677C26.2197 23.2539 26.6677 23.7019 26.6677 24.2539C26.6677 24.8059 26.2197 25.2539 25.6677 25.2539Z",
                fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("mask", {
                id: "mask0_11116_355",
                style: {
                    maskType: "luminance"
                },
                maskUnits: "userSpaceOnUse",
                x: "0",
                y: "0",
                width: "24",
                height: "26",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M0.666992 0H23.5744V25.2527H0.666992V0Z",
                    fill: "white"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                    lineNumber: 19,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                mask: "url(#mask0_11116_355)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    fillRule: "evenodd",
                    clipRule: "evenodd",
                    d: "M15.4807 2.68886L2.92736 18.3889C2.69936 18.6742 2.61536 19.0422 2.69936 19.3955L3.60736 23.2422L7.65936 23.1915C8.04469 23.1875 8.40069 23.0155 8.63669 22.7222C12.926 17.3555 21.1034 7.12352 21.3354 6.82352C21.554 6.46886 21.6394 5.96752 21.5247 5.48486C21.4074 4.99019 21.0994 4.57019 20.6554 4.30219C20.5607 4.23686 18.314 2.49286 18.2447 2.43819C17.3994 1.76086 16.166 1.87819 15.4807 2.68886ZM2.81802 25.2529C2.35536 25.2529 1.95269 24.9355 1.84469 24.4835L0.752691 19.8555C0.527358 18.8969 0.751358 17.9075 1.36602 17.1395L13.926 1.43019C13.9314 1.42486 13.9354 1.41819 13.9407 1.41286C15.318 -0.23381 17.8087 -0.476476 19.4887 0.871523C19.5554 0.923523 21.786 2.65686 21.786 2.65686C22.5967 3.13952 23.23 4.00219 23.47 5.02352C23.7087 6.03419 23.5354 7.07686 22.9794 7.95819C22.938 8.02352 22.902 8.07952 10.198 23.9729C9.58603 24.7355 8.66869 25.1795 7.68336 25.1915L2.83136 25.2529H2.81802Z",
                    fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                    lineNumber: 22,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M19.6316 11.5792C19.4182 11.5792 19.2049 11.5112 19.0222 11.3725L11.7529 5.78851C11.3156 5.45251 11.2329 4.82584 11.5689 4.38584C11.9062 3.94851 12.5329 3.86717 12.9716 4.20317L20.2422 9.78584C20.6796 10.1218 20.7622 10.7498 20.4249 11.1885C20.2289 11.4445 19.9316 11.5792 19.6316 11.5792Z",
                fill: !fillNone ? "#436EB6" : fillColor ? fillColor : ""
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/EditIcon.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = EditIcon;
}}),
"[project]/src/components/svgComponents/DeleteIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function DeleteIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "28",
        viewBox: "0 0 25 28",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = DeleteIcon;
}}),
"[project]/public/assets/images/user-role-img.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/user-role-img.0f1ece75.png");}}),
"[project]/public/assets/images/user-role-img.png.mjs { IMAGE => \"[project]/public/assets/images/user-role-img.png (static in ecmascript)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/assets/images/user-role-img.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 1118,
    height: 1136,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+ABQgM0ZHY5HBIjNPdwkKDCAkJSY7eHl5jq+wsLkkJCQlACIwSGB8mMX1coir8Xd/iNZ7gonZjJGU4XVzcaRIR0ZbACMuQVVidZXG3ePt/+bq8f/o7PL/ztDR6HRkW6lVTkp7AE5olMyPoLn09vf4//b29v/39/f/6OPg+4uGg/RSTUugABIbKThxeIGm8/P0//T09P/4+Pj/2NfW7k9YXN4zOz+fAAAAAABnaWqG8vP1//T09P/19fX/z9DQ6S84PMpETE/CAAAAAABiZGWG6erq/+nq6//S2OH/trvB5yoyNbMXHSCHAAAAAAEjJylsT1Za0VJaXtFGT1fRPkZMvhsiJocTGRxoUJGL9Am/ZT4AAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 8
};
}}),
"[project]/src/components/formElements/InputWrapper.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
}}),
"[project]/src/components/formElements/Textbox.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CommonInput": (()=>CommonInput),
    "default": (()=>Textbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
;
;
function Textbox({ children, control, name, iconClass, align, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${iconClass} ${align}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Controller"], {
                control: control,
                name: name,
                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ...props,
                        value: field.value,
                        onChange: (e)=>{
                            field.onChange(e);
                            props.onChange?.(e);
                        },
                        "aria-label": ""
                    }, void 0, false, {
                        fileName: "[project]/src/components/formElements/Textbox.tsx",
                        lineNumber: 23,
                        columnNumber: 11
                    }, void 0),
                defaultValue: ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textbox.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Textbox.tsx",
        lineNumber: 18,
        columnNumber: 5
    }, this);
}
function CommonInput({ iconClass, children, align, onChange, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${iconClass} ${align}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ...props,
                onChange: onChange
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textbox.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/Textbox.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/svgComponents/SearchIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function SearchIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "40",
        height: "40",
        viewBox: "0 0 40 40",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
            opacity: "0.7",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M28.2109 18.8274C28.2109 20.6833 27.6605 22.4976 26.6295 24.0407C25.5984 25.5839 24.1329 26.7867 22.4182 27.497C20.7036 28.2072 18.8168 28.3931 16.9965 28.0311C15.1762 27.6691 13.5042 26.7755 12.1917 25.4632C10.8793 24.1509 9.9855 22.479 9.62331 20.6587C9.26111 18.8384 9.4468 16.9517 10.1569 15.237C10.867 13.5222 12.0696 12.0566 13.6127 11.0253C15.1557 9.99409 16.9699 9.44356 18.8259 9.44336C20.0583 9.44323 21.2786 9.68586 22.4173 10.1574C23.5559 10.6289 24.5905 11.3201 25.462 12.1915C26.3335 13.0629 27.0248 14.0974 27.4965 15.236C27.9681 16.3746 28.2109 17.595 28.2109 18.8274Z",
                    stroke: "#333333",
                    strokeWidth: "1.5",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/SearchIcon.tsx",
                    lineNumber: 7,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M30.557 30.559L25.457 25.459",
                    stroke: "#333333",
                    strokeWidth: "1.5",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/SearchIcon.tsx",
                    lineNumber: 14,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/svgComponents/SearchIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/SearchIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = SearchIcon;
}}),
"[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const ModalCloseIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "40",
        height: "41",
        viewBox: "0 0 40 41",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "20.0003",
                cy: "20.5",
                r: "18.209",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ModalCloseIcon;
}}),
"[project]/src/services/roleService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addUserRole": (()=>addUserRole),
    "deleteUserRole": (()=>deleteUserRole),
    "findRole": (()=>findRole),
    "findRoleList": (()=>findRoleList),
    "getRolePermissions": (()=>getRolePermissions),
    "getRolePermissionsById": (()=>getRolePermissionsById),
    "updateRolePermissions": (()=>updateRolePermissions),
    "updateUserRole": (()=>updateUserRole)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-ssr] (ecmascript)");
;
;
const findRoleList = (offset, limit)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.GET_ROLES_WITH_PAGINATION, {
        offset,
        limit
    });
};
const findRole = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.GET_ROLES);
};
const addUserRole = (roleData)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.ADD_USER_ROLE, roleData);
};
const updateUserRole = (roleId, roleData)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["put"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.UPDATE_USER_ROLE}/${roleId}`, roleData);
};
const deleteUserRole = (roleId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["remove"])(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.DELETE_USER_ROLE}/${roleId}`);
};
const getRolePermissions = (offset, limit, search)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.GET_ROLE_PERMISSIONS, {
        offset,
        limit,
        search
    });
};
const getRolePermissionsById = (roleId)=>{
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.GET_ROLE_PERMISSIONS_BY_ID.replace(":roleId", roleId.toString());
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["get"])(url);
};
const updateRolePermissions = (roleId, permissionIds)=>{
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].roles.UPDATE_ROLE_PERMISSIONS.replace(":roleId", roleId.toString());
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["put"])(url, {
        permissionIds
    });
};
}}),
"[project]/src/utils/validationSchema.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CANDIDATE_NAME_REGEX": (()=>CANDIDATE_NAME_REGEX),
    "EMAIL_REGEX": (()=>EMAIL_REGEX),
    "NAME_REGEX": (()=>NAME_REGEX),
    "departmentValidationSchema": (()=>departmentValidationSchema),
    "employeeValidationSchema": (()=>employeeValidationSchema),
    "employeesValidationSchema": (()=>employeesValidationSchema),
    "roleValidationSchema": (()=>roleValidationSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yup/index.esm.js [app-ssr] (ecmascript)");
;
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;
const CANDIDATE_NAME_REGEX = /^[\p{L}\s]+$/u;
const employeeValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])().shape({
        firstName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("first_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(1, translation("min_first_name")).max(50, translation("max_first_name")),
        lastName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("last_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(1, translation("min_last_name")).max(50, translation("max_last_name")),
        email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("email_req")).email(translation("email_val_msg")).matches(EMAIL_REGEX, translation("email_val_msg")),
        department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["number"])().transform((value)=>isNaN(value) ? undefined : value).required(translation("department_req")).min(1, "Department must be selected"),
        role: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["number"])().transform((value)=>isNaN(value) ? undefined : value).required(translation("role_req")).min(1, "Role must be selected")
    });
const employeesValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])().shape({
        employees: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["array"])().of(employeeValidationSchema(translation)).required("At least one employee is required")
    });
const departmentValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])().shape({
        name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("department_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(2, translation("min_department_name")).max(50, translation("max_department_name"))
    });
const roleValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["object"])().shape({
        name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("role_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(2, translation("min_role_name")).max(50, translation("max_role_name"))
    });
}}),
"[project]/src/components/views/accessManagement/RolesPermissions.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "ROLE_ALTER_MODE": (()=>ROLE_ALTER_MODE),
    "VIEW_MODE": (()=>VIEW_MODE),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/accessManagement.module.scss.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/EditIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/DeleteIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/assets/images/user-role-img.png.mjs { IMAGE => "[project]/public/assets/images/user-role-img.png (static in ecmascript)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textbox.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$SearchIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/SearchIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$UserRoleModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/UserRoleModal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$EditPermissionsModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/EditPermissionsModal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/roleService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-infinite-scroll-component/dist/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$skeletons$2f$TableSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/skeletons/TableSkeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/debounce.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ROLE_ALTER_MODE = {
    ADD: "add",
    EDIT: "edit",
    DELETE: "delete"
};
const VIEW_MODE = {
    ROLES: "roles",
    PERMISSIONS: "permissions"
};
const RolesPermissions = ()=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const { control } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])();
    const [roleModalConfig, setRoleModalConfig] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        show: false,
        mode: ROLE_ALTER_MODE.ADD,
        role: null
    });
    const [showEditPermissionsModal, setShowEditPermissionsModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedRole, setSelectedRole] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [roles, setRoles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [rolePermissions, setRolePermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoadingRoles, setIsLoadingRoles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isLoadingRolesPermissions, setIsLoadingRolesPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [hasMore, setHasMore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [hasMorePermission, setHasMorePermission] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [activeView, setActiveView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(VIEW_MODE.ROLES);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [offsets, setOffsets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        roles: 0,
        permissions: 0
    });
    // Define fetch functions with useCallback to prevent unnecessary re-renders
    const fetchRoles = async (currentOffset = offsets.roles, reset = false)=>{
        try {
            setIsLoadingRoles(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findRoleList"])(currentOffset, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]);
            if (response?.data?.success && Array.isArray(response.data.data)) {
                const rolesFetched = response.data.data;
                setRoles((prevRoles)=>reset ? rolesFetched : [
                        ...prevRoles,
                        ...rolesFetched
                    ]);
                if (rolesFetched.length < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]) {
                    setHasMore(false);
                } else {
                    setHasMore(true);
                }
                setOffsets((prev)=>({
                        ...prev,
                        roles: currentOffset + rolesFetched.length
                    }));
            } else {
                setHasMore(false);
            }
        } catch (error) {
            console.error("Error fetching roles:", error);
            setHasMore(false);
        } finally{
            setIsLoadingRoles(false);
        }
    };
    const fetchRolePermissions = async (currentOffset = offsets.permissions, search, reset = false)=>{
        try {
            setIsLoadingRolesPermissions(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRolePermissions"])(currentOffset, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"], search);
            if (response.data?.success) {
                const rolePermissionsFetched = response.data.data;
                setRolePermissions((prevRoles)=>reset ? rolePermissionsFetched : [
                        ...prevRoles,
                        ...rolePermissionsFetched
                    ]);
                if (rolePermissionsFetched.length < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LIMIT"]) {
                    setHasMorePermission(false);
                } else {
                    setHasMorePermission(true);
                }
                setOffsets((prev)=>({
                        ...prev,
                        permissions: currentOffset + rolePermissionsFetched.length
                    }));
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(response?.data?.message ? response.data.message : t("failed_load_roles"));
            }
        } catch (error) {
            console.error(error);
            setHasMorePermission(false);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageError"])(t("unexpected_error"));
        } finally{
            setIsLoadingRolesPermissions(false);
        }
    };
    // Fetch data when component mounts or view changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (activeView === VIEW_MODE.ROLES) {
            if (roles.length === 0) {
                fetchRoles(0, true);
            }
        } else {
            if (rolePermissions.length === 0) {
                fetchRolePermissions(0, "", true);
            }
        }
    // Do NOT reset offsets when switching views
    }, [
        activeView
    ]);
    const openRoleModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((mode, role = null)=>{
        setRoleModalConfig({
            show: true,
            mode,
            role
        });
    }, []);
    const closeRoleModal = ()=>{
        setRoleModalConfig({
            show: false,
            mode: ROLE_ALTER_MODE.ADD,
            role: null
        });
    };
    const handleRoleSuccess = (message, responseData)=>{
        if (!responseData) {
            return;
        }
        // Extract the role data from the response
        const roleData = responseData;
        // Update the roles state based on the operation type
        if (roleModalConfig.mode === ROLE_ALTER_MODE.ADD && roleData) {
            // Add the new role to the beginning of the list
            setRoles((prevRoles)=>[
                    {
                        id: roleData.id,
                        name: roleData.name,
                        isDefaultRole: false
                    },
                    ...prevRoles
                ]);
        } else if (roleModalConfig.mode === ROLE_ALTER_MODE.EDIT && roleData) {
            // Update the existing role in the list
            setRoles((prevRoles)=>prevRoles.map((role)=>role.id === roleData.id ? {
                        ...role,
                        name: roleData.name
                    } : role));
        } else if (roleModalConfig.mode === ROLE_ALTER_MODE.DELETE && roleData) {
            // Remove the deleted role from the list
            setRoles((prevRoles)=>prevRoles.filter((role)=>role.id !== roleData.id));
        }
        if (message) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(message);
        }
    };
    const handleEditClick = (role)=>{
        openRoleModal(ROLE_ALTER_MODE.EDIT, role);
    };
    const handleDeleteClick = (role)=>{
        openRoleModal(ROLE_ALTER_MODE.DELETE, role);
    };
    const handlePermissionsClick = (role)=>{
        setSelectedRole(role);
        setShowEditPermissionsModal(true);
    };
    const handlePermissionsSuccess = (message, updatedRole)=>{
        if (updatedRole) {
            // Update the role in the rolePermissions array without making an API call
            setRolePermissions((prevRoles)=>prevRoles.map((role)=>role.id === updatedRole.id ? {
                        ...role,
                        name: updatedRole.name,
                        permission_count: updatedRole.permission_count,
                        updated_ts: updatedRole.updated_ts,
                        isDefaultRole: !!updatedRole.isDefaultRole
                    } : role));
        } else {
            // If no updated role data is provided, fetch from API as before
            if (activeView === VIEW_MODE.ROLES) {
                fetchRoles(offsets.roles);
            } else {
                fetchRolePermissions(offsets.permissions, searchTerm);
            }
        }
        if (message) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(message);
        }
    };
    const handleSearchInputChange = (event)=>{
        const searchString = event.trim();
        setSearchTerm(searchString);
        setRolePermissions([]); // Clear previous results on new search
        setHasMorePermission(true);
        setOffsets((prev)=>({
                ...prev,
                permissions: 0
            }));
        fetchRolePermissions(0, searchString, true);
    };
    const debouncedHandleSearchInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(handleSearchInputChange, 1000);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].access_management,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "row",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: activeView === VIEW_MODE.ROLES ? "col-md-7" : "col-md-12",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "common-page-head-section",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "main-heading",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                children: [
                                                    t("access_management", {
                                                        defaultValue: "Access"
                                                    }),
                                                    " ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: t("management", {
                                                            defaultValue: "Management"
                                                        })
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 243,
                                                        columnNumber: 74
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 242,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                            lineNumber: 241,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                        lineNumber: 240,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "button-align justify-content-between mt-5 mb-5",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "button-align",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        className: `${activeView === VIEW_MODE.ROLES ? "primary-btn" : "dark-outline-btn"} rounded-md button-sm minWidth`,
                                                        onClick: ()=>setActiveView(VIEW_MODE.ROLES),
                                                        children: t("user_roles", {
                                                            defaultValue: "User Roles"
                                                        })
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 249,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        className: `${activeView === VIEW_MODE.PERMISSIONS ? "primary-btn" : "dark-outline-btn"} rounded-md button-sm`,
                                                        onClick: ()=>setActiveView(VIEW_MODE.PERMISSIONS),
                                                        children: t("user_permissions", {
                                                            defaultValue: "User Permissions"
                                                        })
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 255,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 248,
                                                columnNumber: 17
                                            }, this),
                                            activeView === VIEW_MODE.PERMISSIONS && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                className: "mb-0 w-50",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "icon-align right",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        className: "form-control w-100",
                                                        control: control,
                                                        name: "search",
                                                        type: "text",
                                                        placeholder: t("search_user_role", {
                                                            defaultValue: "Search using user role"
                                                        }),
                                                        onChange: (e)=>debouncedHandleSearchInputChange(e.target.value),
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Icon, {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$SearchIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                lineNumber: 275,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 274,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 266,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                    lineNumber: 265,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 264,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                        lineNumber: 247,
                                        columnNumber: 15
                                    }, this),
                                    activeView === VIEW_MODE.ROLES ? // Roles View
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "common-card margin-add",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "card-header",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        children: t("user_role")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 287,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        className: "dark-outline-btn rounded-md",
                                                        onClick: ()=>openRoleModal(ROLE_ALTER_MODE.ADD),
                                                        children: t("add_new_role")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 288,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 286,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "card-body",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    dataLength: roles.length,
                                                    next: ()=>fetchRoles(offsets.roles),
                                                    hasMore: hasMore,
                                                    height: window.innerHeight - 500,
                                                    className: "pe-4",
                                                    loader: isLoadingRoles && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: `mt-3 ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_roles}`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            height: 30,
                                                            width: "100%",
                                                            count: 6,
                                                            borderRadius: 4,
                                                            style: {
                                                                margin: "10px 0"
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 303,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 302,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    endMessage: !isLoadingRoles && roles.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center py-4",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            children: t("no_more_roles_to_fetch")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 310,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 309,
                                                        columnNumber: 29
                                                    }, void 0) : null,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_roles,
                                                        children: roles.length ? [
                                                            ...roles
                                                        ].sort((a, b)=>a.name.localeCompare(b.name)).map((role)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                className: role.isDefaultRole ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].disabled_role : "",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        children: role.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 321,
                                                                        columnNumber: 37
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "button-align",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                className: `clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`,
                                                                                onClick: ()=>!role.isDefaultRole && handleEditClick(role),
                                                                                disabled: role.isDefaultRole,
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$EditIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                                    lineNumber: 328,
                                                                                    columnNumber: 41
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                                lineNumber: 323,
                                                                                columnNumber: 39
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                className: `clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`,
                                                                                onClick: ()=>!role.isDefaultRole && handleDeleteClick(role),
                                                                                disabled: role.isDefaultRole,
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                                    lineNumber: 335,
                                                                                    columnNumber: 41
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                                lineNumber: 330,
                                                                                columnNumber: 39
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 322,
                                                                        columnNumber: 37
                                                                    }, this)
                                                                ]
                                                            }, role.id, true, {
                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                lineNumber: 320,
                                                                columnNumber: 35
                                                            }, this)) : !isLoadingRoles && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-center p-5",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: t("no_roles_found")
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                lineNumber: 342,
                                                                columnNumber: 35
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 341,
                                                            columnNumber: 33
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 315,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                    lineNumber: 294,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 292,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                        lineNumber: 285,
                                        columnNumber: 17
                                    }, this) : // Roles View End
                                    // Permissions View
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "table-responsive mt-5",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$infinite$2d$scroll$2d$component$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            dataLength: rolePermissions.length,
                                            next: ()=>fetchRolePermissions(offsets.permissions, searchTerm),
                                            hasMore: hasMorePermission,
                                            height: window.innerHeight - 300,
                                            loader: isLoadingRolesPermissions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                className: "table w-100",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$skeletons$2f$TableSkeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    rows: 10,
                                                    cols: 4,
                                                    colWidths: "120,80,100,24,24"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                    lineNumber: 362,
                                                    columnNumber: 27
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 361,
                                                columnNumber: 25
                                            }, void 0),
                                            endMessage: !isLoadingRolesPermissions && rolePermissions.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                className: "table w-100",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            colSpan: 5,
                                                            style: {
                                                                textAlign: "center",
                                                                backgroundColor: "#fff"
                                                            },
                                                            children: t("no_more_roles_to_fetch")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 370,
                                                        columnNumber: 29
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                    lineNumber: 369,
                                                    columnNumber: 27
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 368,
                                                columnNumber: 25
                                            }, void 0) : null,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                className: "table overflow-auto mb-0",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                    style: {
                                                                        width: "25%"
                                                                    },
                                                                    children: t("user_role")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                    lineNumber: 383,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                    style: {
                                                                        width: "25%"
                                                                    },
                                                                    className: "text-center",
                                                                    children: t("permission_counts")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                    lineNumber: 384,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                    style: {
                                                                        width: "25%"
                                                                    },
                                                                    className: "text-center",
                                                                    children: t("last_modified")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                    lineNumber: 387,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                    style: {
                                                                        width: "25%"
                                                                    },
                                                                    className: "text-center",
                                                                    children: t("actions")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                    lineNumber: 390,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 382,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 381,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                        children: rolePermissions.length ? [
                                                            ...rolePermissions
                                                        ].sort((a, b)=>a.name.localeCompare(b.name)).map((role)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                                className: role.isDefaultRole ? "text-muted opacity-75 disabled-row" : "",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                        style: {
                                                                            width: "25%"
                                                                        },
                                                                        children: role.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 401,
                                                                        columnNumber: 35
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                        style: {
                                                                            width: "25%"
                                                                        },
                                                                        className: "text-center",
                                                                        children: role.permission_count
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 402,
                                                                        columnNumber: 35
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                        style: {
                                                                            width: "25%"
                                                                        },
                                                                        className: "text-center",
                                                                        children: new Date(role.updated_ts).toLocaleDateString("en-US", {
                                                                            month: "2-digit",
                                                                            day: "2-digit",
                                                                            year: "numeric"
                                                                        })
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 405,
                                                                        columnNumber: 35
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                        style: {
                                                                            width: "25%"
                                                                        },
                                                                        className: "text-center",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                            className: `clear-btn p-0 text-decoration-underline m-auto ${role.isDefaultRole ? "text-muted" : "color-primary"}`,
                                                                            onClick: ()=>!role.isDefaultRole && handlePermissionsClick({
                                                                                    id: role.id,
                                                                                    name: role.name,
                                                                                    isDefaultRole: role.isDefaultRole
                                                                                }),
                                                                            disabled: role.isDefaultRole,
                                                                            style: role.isDefaultRole ? {
                                                                                cursor: "not-allowed"
                                                                            } : {},
                                                                            children: t("edit")
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                            lineNumber: 409,
                                                                            columnNumber: 37
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 408,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                ]
                                                            }, role.id, true, {
                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                lineNumber: 400,
                                                                columnNumber: 33
                                                            }, this)) : !isLoadingRolesPermissions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                colSpan: 5,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-center",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                        children: t("no_roles_found")
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                        lineNumber: 427,
                                                                        columnNumber: 37
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                    lineNumber: 426,
                                                                    columnNumber: 35
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                                lineNumber: 425,
                                                                columnNumber: 33
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                            lineNumber: 424,
                                                            columnNumber: 31
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                        lineNumber: 395,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                                lineNumber: 380,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                            lineNumber: 354,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                        lineNumber: 353,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                lineNumber: 239,
                                columnNumber: 13
                            }, this),
                            activeView === VIEW_MODE.ROLES && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "col-md-5",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$assets$2f$images$2f$user$2d$role$2d$img$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                    alt: t("user_roles_alt"),
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$accessManagement$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].user_roles_img
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                    lineNumber: 442,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                                lineNumber: 441,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                        lineNumber: 238,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                    lineNumber: 237,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                lineNumber: 236,
                columnNumber: 7
            }, this),
            roleModalConfig.show && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$UserRoleModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: closeRoleModal,
                onSubmitSuccess: handleRoleSuccess,
                role: roleModalConfig.role,
                mode: roleModalConfig.mode,
                disabled: isLoadingRoles
            }, void 0, false, {
                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                lineNumber: 450,
                columnNumber: 9
            }, this),
            showEditPermissionsModal && selectedRole && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$EditPermissionsModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: ()=>setShowEditPermissionsModal(false),
                onSubmitSuccess: handlePermissionsSuccess,
                role: selectedRole,
                disabled: isLoadingRolesPermissions
            }, void 0, false, {
                fileName: "[project]/src/components/views/accessManagement/RolesPermissions.tsx",
                lineNumber: 460,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = RolesPermissions;
}}),
"[project]/src/components/commonModals/UserRoleModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textbox.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/yup/dist/yup.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/roleService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validationSchema$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validationSchema.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/accessManagement/RolesPermissions.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const UserRoleModal = ({ onClickCancel, onSubmitSuccess, disabled, role, mode })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [submitError, setSubmitError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isNameChanged, setIsNameChanged] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { control, handleSubmit, formState: { errors, isValid } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        defaultValues: {
            name: role?.name || ""
        },
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["yupResolver"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validationSchema$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["roleValidationSchema"])(t)),
        mode: "onChange"
    });
    // Watch for changes in the name field
    const currentName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control,
        name: "name",
        defaultValue: role?.name || ""
    });
    // Update isNameChanged when the name changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT && role) {
            const normalizedCurrentName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(currentName);
            const normalizedOriginalName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(role.name);
            setIsNameChanged(normalizedCurrentName !== normalizedOriginalName && normalizedCurrentName !== "");
        }
    }, [
        currentName,
        role,
        mode
    ]);
    const onSubmit = async (data)=>{
        try {
            setIsSubmitting(true);
            setSubmitError(null);
            const requestData = {
                name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(data.name)
            };
            let response;
            try {
                if (mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].ADD) {
                    response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addUserRole"])(requestData);
                } else if (mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT && role) {
                    response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateUserRole"])(role.id, requestData);
                } else if (mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].DELETE && role) {
                    response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteUserRole"])(role.id);
                } else {
                    throw new Error(t("unexpected_error"));
                }
                const result = response.data;
                if (result && result.success) {
                    // Call onSubmitSuccess with the success message and response data
                    const actionType = mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].ADD ? t("added") : mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT ? t("updated") : t("deleted");
                    const successMessage = t("role_action_success", {
                        actionType: actionType
                    });
                    onSubmitSuccess(successMessage, result.data);
                    onClickCancel();
                } else {
                    const errorMessage = t(result.message || "failed_role_operation");
                    setSubmitError(errorMessage);
                }
            } catch (error) {
                console.error(error);
                const apiError = response?.error;
                const errorMessage = apiError?.status === 401 ? t("authentication_error") : apiError?.message || t("failed_role_operation");
                setSubmitError(errorMessage);
            }
        } catch (error) {
            console.error(error);
            setSubmitError(t("unexpected_error"));
        } finally{
            setIsSubmitting(false);
        }
    };
    const getModalTitle = ()=>{
        switch(mode){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].ADD:
                return t("add_role");
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT:
                return t("edit_role");
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].DELETE:
                return t("delete_role");
            default:
                return t("role");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-center pb-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "m-0",
                                children: getModalTitle()
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                disabled: isSubmitting,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 120,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                lineNumber: 119,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        children: mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].DELETE ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-center mb-4",
                                    children: t("confirm_delete_role", {
                                        roleName: role?.name || ""
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 126,
                                    columnNumber: 17
                                }, this),
                                submitError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "alert alert-danger mb-3",
                                    role: "alert",
                                    children: submitError
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 129,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "button-align mt-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            type: "button",
                                            className: "danger-btn rounded-md w-100",
                                            onClick: handleSubmit(onSubmit),
                                            disabled: isSubmitting || disabled,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "d-flex align-items-center justify-content-center",
                                                children: [
                                                    isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                        lineNumber: 137,
                                                        columnNumber: 40
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: isSubmitting ? "ms-2" : "",
                                                        children: t("delete_role")
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                        lineNumber: 138,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                lineNumber: 136,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 135,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            type: "button",
                                            className: "dark-outline-btn rounded-md w-100",
                                            onClick: onClickCancel,
                                            disabled: isSubmitting || disabled,
                                            children: t("cancel")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 141,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 134,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                            lineNumber: 125,
                            columnNumber: 15
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                            onSubmit: handleSubmit(onSubmit),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Label, {
                                            htmlFor: "name",
                                            required: true,
                                            className: "fw-bold",
                                            children: t("role_name")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 149,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            className: "form-control",
                                            control: control,
                                            name: "name",
                                            type: "text",
                                            placeholder: t("enter_role_name"),
                                            disabled: isSubmitting || disabled
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 152,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Error, {
                                            message: errors?.name?.message || ""
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 160,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 148,
                                    columnNumber: 17
                                }, this),
                                submitError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "alert alert-danger mb-3",
                                    role: "alert",
                                    children: submitError
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 164,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "button-align mt-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            type: "submit",
                                            className: `primary-btn rounded-md w-100 ${isSubmitting || disabled || mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT && !isNameChanged || !isValid || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(currentName) === "" ? "truly-disabled" : ""}`,
                                            disabled: isSubmitting || disabled || mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT && !isNameChanged || !isValid || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(currentName) === "",
                                            title: mode === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$accessManagement$2f$RolesPermissions$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ROLE_ALTER_MODE"].EDIT && !isNameChanged ? t("change_role_name_hint") : (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSpaces"])(currentName) === "" ? t("role_name_req") : "",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "d-flex align-items-center justify-content-center",
                                                children: [
                                                    isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                        lineNumber: 185,
                                                        columnNumber: 40
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: isSubmitting ? "ms-2" : "",
                                                        children: getModalTitle()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                        lineNumber: 186,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                                lineNumber: 184,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 170,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            type: "button",
                                            className: "dark-outline-btn rounded-md w-100",
                                            onClick: onClickCancel,
                                            disabled: isSubmitting || disabled,
                                            children: t("cancel")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                            lineNumber: 189,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                                    lineNumber: 169,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                            lineNumber: 147,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                        lineNumber: 123,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
                lineNumber: 115,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
            lineNumber: 114,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/UserRoleModal.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = UserRoleModal;
}}),
"[project]/src/components/svgComponents/RefreshAlertIcon.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const RefreshAlertIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "32",
        height: "32",
        viewBox: "0 0 24 24",
        fill: "none",
        className: className,
        stroke: "#000000",
        "stroke-width": "1",
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RefreshAlertIcon.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RefreshAlertIcon.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12 9l0 3"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RefreshAlertIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12 15l.01 0"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RefreshAlertIcon.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/RefreshAlertIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = RefreshAlertIcon;
}}),
"[project]/src/components/commonModals/EditPermissionsModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/roleService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RefreshAlertIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/RefreshAlertIcon.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
const EditPermissionsModal = ({ onClickCancel, onSubmitSuccess, disabled, role })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [submitError, setSubmitError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loadError, setLoadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [permissions, setPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [originalPermissions, setOriginalPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [hasChanges, setHasChanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasAtLeastOneSelected, setHasAtLeastOneSelected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [roleName, setRoleName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(role.name);
    // Fetch permissions from API when component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchPermissions = async ()=>{
            try {
                setIsLoading(true);
                setLoadError(null);
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRolePermissionsById"])(role.id);
                const data = response.data;
                if (data.success && data.data) {
                    setRoleName(data.data.role_name);
                    // Transform API response to component state format
                    const transformedPermissions = data.data.permissions.map((permission)=>({
                            id: permission.id,
                            name: permission.name,
                            description: permission.description,
                            checked: permission.selected || false
                        }));
                    setPermissions(transformedPermissions);
                    setOriginalPermissions(transformedPermissions);
                    // Check if at least one permission is selected
                    const hasSelected = transformedPermissions.some((p)=>p.checked);
                    setHasAtLeastOneSelected(hasSelected);
                } else {
                    console.error(t("failed_load_permissions"));
                    setLoadError(t("failed_load_permissions"));
                }
            } catch (error) {
                console.error(error);
                setLoadError(t("unexpected_error"));
            } finally{
                setIsLoading(false);
            }
        };
        fetchPermissions();
    }, [
        role.id
    ]);
    const handleSelectAll = (e)=>{
        const isChecked = e.target.checked;
        const updatedPermissions = permissions.map((permission)=>({
                ...permission,
                checked: isChecked
            }));
        setPermissions(updatedPermissions);
        // Check if permissions have changed from original state
        const hasAnyChanges = updatedPermissions.some((updatedPerm, index)=>updatedPerm.checked !== originalPermissions[index].checked);
        setHasChanges(hasAnyChanges);
        // Update whether at least one permission is selected
        setHasAtLeastOneSelected(isChecked);
    };
    const handlePermissionChange = (id, checked)=>{
        const updatedPermissions = permissions.map((permission)=>permission.id === id ? {
                ...permission,
                checked
            } : permission);
        setPermissions(updatedPermissions);
        // Check if any permission has changed from its original state
        const hasAnyChanges = updatedPermissions.some((updatedPerm)=>{
            const originalPerm = originalPermissions.find((p)=>p.id === updatedPerm.id);
            return originalPerm && originalPerm.checked !== updatedPerm.checked;
        });
        setHasChanges(hasAnyChanges);
        // Check if at least one permission is selected
        const hasSelected = updatedPermissions.some((p)=>p.checked);
        setHasAtLeastOneSelected(hasSelected);
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        try {
            setIsSubmitting(true);
            setSubmitError(null);
            // Get only the IDs of checked permissions
            const permissionIds = permissions.reduce((acc, p)=>{
                if (p.checked) acc.push(p.id);
                return acc;
            }, []);
            // Make the API call to update role permissions with only permissionIds
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateRolePermissions"])(role.id, permissionIds);
            const data = response.data;
            if (data.success) {
                // Call the success callback with success message and updated role data
                onSubmitSuccess(t("permissions_updated_success"), data.data);
                // Close the modal
                onClickCancel();
            } else {
                console.error(data.message);
                setSubmitError(data.message || t("failed_update_permissions"));
            }
        } catch (error) {
            console.error(error);
            setSubmitError(t("unexpected_error"));
        } finally{
            setIsSubmitting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered modal-lg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-center pb-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "m-0",
                                children: [
                                    t("edit_permissions_for"),
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: roleName
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                        lineNumber: 141,
                                        columnNumber: 43
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 140,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                disabled: isSubmitting,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                    lineNumber: 144,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 143,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                        lineNumber: 139,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body pt-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mb-5 text-center px-3",
                                children: t("permissions_description")
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 148,
                                columnNumber: 13
                            }, this),
                            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "permissions-card p-5 w-100 mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    height: 20,
                                    width: "100%",
                                    borderRadius: 4,
                                    count: 8,
                                    className: "mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                    lineNumber: 151,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 150,
                                columnNumber: 15
                            }, this) : loadError ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "alert alert-danger mb-3",
                                role: "alert",
                                children: [
                                    loadError,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "clear-btn p-0",
                                        onClick: ()=>{
                                            setIsLoading(true);
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$roleService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRolePermissionsById"])(role.id).then((response)=>{
                                                const data = response.data;
                                                if (data.success && data.data) {
                                                    setRoleName(data.data.role_name);
                                                    const transformedPermissions = data.data.permissions.map((permission)=>({
                                                            id: permission.id,
                                                            name: permission.name,
                                                            description: permission.description,
                                                            checked: permission.selected || false
                                                        }));
                                                    setPermissions(transformedPermissions);
                                                    setLoadError(null);
                                                } else {
                                                    setLoadError(t("failed_load_permissions"));
                                                }
                                            }).catch((error)=>{
                                                console.error("Error fetching permissions:", error);
                                                setLoadError(t("unexpected_error"));
                                            }).finally(()=>setIsLoading(false));
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RefreshAlertIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                            lineNumber: 184,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 154,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                onSubmit: handleSubmit,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "permissions-card p-5",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                children: t("role_permissions")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                lineNumber: 190,
                                                columnNumber: 19
                                            }, this),
                                            permissions.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "container-checkbox",
                                                        children: [
                                                            t("select_all"),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "checkbox",
                                                                onChange: handleSelectAll,
                                                                checked: permissions.length > 0 && permissions.every((p)=>p.checked),
                                                                disabled: isSubmitting || disabled
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                lineNumber: 195,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "checkmark"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                lineNumber: 201,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                        lineNumber: 193,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "checbox-group",
                                                        children: permissions.map((permission)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                    className: "container-checkbox",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "permission-item",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "permission-name",
                                                                                    children: permission.name
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                                    lineNumber: 208,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "permission-description",
                                                                                    children: permission.description
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                                    lineNumber: 209,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                            lineNumber: 207,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                            type: "checkbox",
                                                                            checked: permission.checked,
                                                                            onChange: (e)=>handlePermissionChange(permission.id, e.target.checked),
                                                                            disabled: isSubmitting || disabled
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                            lineNumber: 211,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "checkmark"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                            lineNumber: 217,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                    lineNumber: 206,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, permission.id, false, {
                                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                                lineNumber: 205,
                                                                columnNumber: 27
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                        lineNumber: 203,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-center py-3",
                                                children: t("no_permissions_found")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                lineNumber: 224,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                        lineNumber: 189,
                                        columnNumber: 17
                                    }, this),
                                    submitError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "alert alert-danger mb-3",
                                        role: "alert",
                                        children: submitError
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                        lineNumber: 229,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "button-align mt-5",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                type: "button",
                                                className: "dark-outline-btn rounded-md w-100",
                                                onClick: onClickCancel,
                                                disabled: isSubmitting || disabled,
                                                children: t("cancel_department_edit")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                lineNumber: 235,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                type: "submit",
                                                className: `primary-btn rounded-md w-100 ${isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected ? "truly-disabled" : ""}`,
                                                disabled: isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected,
                                                title: !hasChanges ? t("make_changes_to_enable_save") : !hasAtLeastOneSelected ? t("at_least_one_permission") : "",
                                                children: isSubmitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 25
                                                        }, this),
                                                        " ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "ms-2",
                                                            children: t("saving")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 36
                                                        }, this)
                                                    ]
                                                }, void 0, true) : t("save_permissions")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                                lineNumber: 238,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                        lineNumber: 234,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                                lineNumber: 188,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                        lineNumber: 147,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
                lineNumber: 138,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
            lineNumber: 137,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/EditPermissionsModal.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = EditPermissionsModal;
}}),
"[project]/src/components/views/skeletons/TableSkeleton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-ssr] (ecmascript)");
;
;
;
const TableSkeleton = ({ rows = 3, cols = 3, colWidths = "120,80,100" })=>{
    const columnWidths = colWidths.split(",").map((w)=>w.trim());
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
        children: [
            ...Array(rows)
        ].map((_, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                children: [
                    ...Array(cols)
                ].map((_, colIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                        className: "text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            width: columnWidths[colIndex] || 80,
                            height: 20,
                            circle: false
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                            lineNumber: 14,
                            columnNumber: 15
                        }, this)
                    }, `loader-col-${colIndex}`, false, {
                        fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                        lineNumber: 13,
                        columnNumber: 13
                    }, this))
            }, `loader-row-${rowIndex}`, false, {
                fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
                lineNumber: 11,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/views/skeletons/TableSkeleton.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TableSkeleton;
}}),

};

//# sourceMappingURL=_183bceeb._.js.map